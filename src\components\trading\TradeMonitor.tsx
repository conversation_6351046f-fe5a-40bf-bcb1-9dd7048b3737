import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Radar, X, Eye, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Trade } from '@/types/trading';
import { calculateStopLossPrice, calculateTakeProfitPrice } from '@/utils/tradingCalculations';
import { useAuth } from '@/contexts/AuthContext';

interface TradeMonitorProps {
    trades: Trade[];
    closeTrade: (tradeId: string, additionalInfo?: {
        closeReason?: 'stop_loss' | 'take_profit' | 'emergency_stop_loss' | null,
        profitLossPercent?: number,
        isAutomatic?: boolean,
        fastPriceMovement?: boolean
    }) => Promise<void>;
    stopLoss: number;
    profitTarget: number;
    leverage: number;
    getTradePriceForSymbol: (symbol: string) => number;
    monitoringFrequency?: number;
    credentials?: {
        apiKey: string;
        apiSecret?: string;
    };
    onUpdateTrade?: (updatedTrade: Trade) => void;
    onUpdateBalance?: (balances: any[]) => void;
}

const TradeMonitor: React.FC<TradeMonitorProps> = ({
    trades,
    closeTrade,
    stopLoss,
    profitTarget,
    leverage,
    getTradePriceForSymbol,
    monitoringFrequency = 1000,
    credentials,
    onUpdateTrade,
    onUpdateBalance
}) => {
    const [openOrders, setOpenOrders] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [showOrders, setShowOrders] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Get current user's credentials file
    const { currentUser } = useAuth();

    // API çağrıları için base URL
    const BASE_URL = 'https://parabot.fun:3001';

    // 🔧 YENİ: Tüm açık pozisyonları kapat
    const closeAllPositions = async () => {
        try {
            setLoading(true);
            setError(null);

            // Önce açık pozisyonları al
            const positionsResponse = await fetch(`${BASE_URL}/api/binance/proxy`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v2/positionRisk',
                    method: 'GET',
                    headers: {
                        'X-MBX-APIKEY': 'SESSION_BASED'
                    },
                    params: {}
                })
            });

            if (!positionsResponse.ok) {
                throw new Error(`HTTP error! status: ${positionsResponse.status}`);
            }

            const positionsData = await positionsResponse.json();
            const openPositions = positionsData.result?.filter((pos: any) =>
                parseFloat(pos.positionAmt || '0') !== 0
            ) || [];

            console.log('📊 Açık pozisyonlar:', openPositions);

            if (openPositions.length === 0) {
                setError('Kapatılacak açık pozisyon bulunamadı.');
                return;
            }

            // Her pozisyonu kapat
            let closedCount = 0;
            for (const position of openPositions) {
                const quantity = Math.abs(parseFloat(position.positionAmt)).toString();
                const side = parseFloat(position.positionAmt) > 0 ? 'SELL' : 'BUY';

                console.log(`🔄 ${position.symbol} pozisyonu kapatılıyor: ${side} ${quantity}`);

                try {
                    const closeResponse = await fetch(`${BASE_URL}/api/binance/proxy`, {
                        method: 'POST',
                        credentials: 'include',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            endpoint: '/fapi/v1/order',
                            method: 'POST',
                            headers: {
                                'X-MBX-APIKEY': 'SESSION_BASED'
                            },
                            params: {
                                symbol: position.symbol,
                                side: side,
                                type: 'MARKET',
                                quantity: quantity,
                                reduceOnly: true
                            }
                        })
                    });

                    if (closeResponse.ok) {
                        const closeData = await closeResponse.json();
                        console.log(`✅ ${position.symbol} pozisyonu kapatıldı:`, closeData);
                        closedCount++;
                    } else {
                        console.error(`❌ ${position.symbol} pozisyonu kapatılamadı:`, closeResponse.status);
                    }
                } catch (error) {
                    console.error(`❌ ${position.symbol} pozisyon kapatma hatası:`, error);
                }
            }

            console.log(`✅ ${closedCount}/${openPositions.length} pozisyon kapatıldı`);

        } catch (error) {
            console.error('❌ Pozisyon kapatma hatası:', error);
            setError(error instanceof Error ? error.message : 'Bilinmeyen hata');
        } finally {
            setLoading(false);
        }
    };

    // Tüm açık emirleri iptal et
    const cancelAllOrders = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await fetch(`${BASE_URL}/api/binance/proxy`, {
                method: 'POST',
                credentials: 'include', // 🔧 DÜZELTME: Session cookie'leri dahil et
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/allOpenOrders',
                    method: 'DELETE',
                    headers: {
                        'X-MBX-APIKEY': 'SESSION_BASED' // 🔧 DÜZELTME: Server will use session credentials
                    },
                    params: {}
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('✅ Tüm emirler iptal edildi:', data);

            // Başarılı olursa açık emirleri yenile
            await fetchOpenOrders();

        } catch (error) {
            console.error('❌ Emir iptal hatası:', error);
            setError(`Emir iptal hatası: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    // Açık emirleri getir
    const fetchOpenOrders = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await fetch(`${BASE_URL}/api/binance/proxy`, {
                method: 'POST',
                credentials: 'include', // 🔧 DÜZELTME: Session cookie'leri dahil et
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/openOrders',
                    method: 'GET',
                    headers: {
                        'X-MBX-APIKEY': 'SESSION_BASED' // 🔧 DÜZELTME: Server will use session credentials
                    },
                    params: {}
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('✅ Açık emirler alındı:', data);
            setOpenOrders(data.result || []);
            setShowOrders(true);

        } catch (error) {
            console.error('❌ Açık emir alma hatası:', error);
            setError(`Açık emir alma hatası: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    // Error'ı temizle
    const clearError = () => {
        setError(null);
    };

    const openTradesCount = trades.filter(trade => trade.status === 'open').length;

    return (
        <Card className="shadow-sm">
            <CardHeader className="pb-1 pt-2">
                <CardTitle className="text-sm flex items-center space-x-2">
                    <Radar className="h-4 w-4 text-blue-500" />
                    <span>İşlem Monitörü</span>
                    <Badge variant="outline">
                        {openTradesCount} Açık İşlem
                    </Badge>
                </CardTitle>
                <CardDescription className="text-xs">
                    Pozisyon yönetimi ve açık emirler
                </CardDescription>
            </CardHeader>
            <CardContent className="pt-1">
                {error && (
                    <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <AlertTriangle className="h-4 w-4 text-red-500" />
                                <span className="text-xs text-red-700">{error}</span>
                            </div>
                            <Button
                                size="sm"
                                variant="ghost"
                                onClick={clearError}
                                className="h-6 w-6 p-0"
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        </div>
                    </div>
                )}

                <div className="space-y-2">
                    <div className="flex space-x-2">
                        <Button
                            size="sm"
                            variant="destructive"
                            onClick={closeAllPositions}
                            disabled={loading}
                            className="flex-1"
                        >
                            <X className="h-3 w-3 mr-1" />
                            {loading ? 'Kapatılıyor...' : 'Tüm Pozisyonları Kapat'}
                        </Button>

                        <Button
                            size="sm"
                            variant="outline"
                            onClick={cancelAllOrders}
                            disabled={loading}
                            className="flex-1"
                        >
                            <X className="h-3 w-3 mr-1" />
                            {loading ? 'İptal Ediliyor...' : 'Tüm Emirleri İptal'}
                        </Button>

                        <Button
                            size="sm"
                            variant="outline"
                            onClick={fetchOpenOrders}
                            disabled={loading}
                            className="flex-1"
                        >
                            <Eye className="h-3 w-3 mr-1" />
                            {loading ? 'Yükleniyor...' : 'Açık Pozisyonları Gör'}
                        </Button>
                    </div>

                    {showOrders && (
                        <div className="mt-3">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-xs font-medium">Açık Emirler ({openOrders.length})</span>
                                <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => setShowOrders(false)}
                                    className="h-6 w-6 p-0"
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            </div>

                            {openOrders.length === 0 ? (
                                <div className="text-center py-4 text-xs text-muted-foreground">
                                    Açık emir bulunmuyor
                                </div>
                            ) : (
                                <div className="space-y-2 max-h-40 overflow-y-auto">
                                    {openOrders.map((order, index) => (
                                        <div key={order.orderId || index} className="p-2 bg-muted rounded text-xs">
                                            <div className="flex justify-between items-center">
                                                <span className="font-mono font-medium">{order.symbol}</span>
                                                <Badge variant={order.side === 'BUY' ? 'default' : 'destructive'} className="text-xs">
                                                    {order.side}
                                                </Badge>
                                            </div>
                                            <div className="grid grid-cols-2 gap-1 mt-1 text-xs text-muted-foreground">
                                                <div>Tip: {order.type}</div>
                                                <div>Miktar: {parseFloat(order.origQty).toFixed(4)}</div>
                                                {order.price && (
                                                    <>
                                                        <div>Fiyat: ${parseFloat(order.price).toFixed(2)}</div>
                                                        <div>Durum: {order.status}</div>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
};

export default TradeMonitor; 
