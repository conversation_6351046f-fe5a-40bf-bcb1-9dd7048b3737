import express from 'express';
import cors from 'cors';
import session from 'express-session';
import path from 'path';
import crypto from 'crypto';
import fs from 'fs';
import https from 'https';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import fetch from 'node-fetch';
import { sign, etc } from "@noble/ed25519";
import { sha512 } from "@noble/hashes/sha512";
etc.sha512Sync = sha512;

import { TextEncoder } from 'util';

// Binance API sabitleri
const BINANCE_FUTURES_BASE_URL = "https://fapi.binance.com";

// ESM için __dirname ve __filename equivalent
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load users configuration
let USERS = [];
let API_KEYS = {};

// Load users.json
try {
    const usersPath = path.join(__dirname, 'src', 'config', 'users.json');
    const usersData = fs.readFileSync(usersPath, 'utf8');
    const usersConfig = JSON.parse(usersData);
    USERS = usersConfig.users || [];
    console.log('✅ Users configuration loaded:', USERS.length, 'users');
} catch (error) {
    console.error('❌ Error loading users.json:', error);
    process.exit(1);
}

// Load binance-key.json
try {
    const apiKeysPath = path.join(__dirname, 'src', 'config', 'binance-key.json');
    const apiKeysData = fs.readFileSync(apiKeysPath, 'utf8');
    const apiKeysConfig = JSON.parse(apiKeysData);
    // Convert array format to object format for easier lookup
    if (apiKeysConfig.users && Array.isArray(apiKeysConfig.users)) {
        apiKeysConfig.users.forEach(user => {
            API_KEYS[user.username] = {
                apiKey: user.apiKey,
                apiSecret: user.apiSecret,
                ed25519ApiKey: user.ed25519ApiKey,
                privateKeyHex: user.privateKeyHex
            };
        });
    }
    console.log('✅ API keys configuration loaded for users:', Object.keys(API_KEYS));
} catch (error) {
    console.error('❌ Error loading binance-key.json:', error);
    process.exit(1);
}

// Note: Credential loading is now handled through binance-key.json
// The API_KEYS object above contains all user credentials

const app = express();
const PORT = 3001;

// ÖNEMLİ: CORS middleware'i tüm route'lardan ÖNCE gelmelidir!
app.use(cors({
    origin: ['https://parabot.fun', 'http://localhost:5173', 'http://localhost:8080', 'http://***********:8080'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-MBX-APIKEY', 'X-Requested-With']
}));

// OPTIONS istekleri için özel yanıt
app.options('*', cors());

// Session middleware
app.use(session({
    secret: 'crypto-future-streamer-secret-key-2024',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Set to true if using HTTPS
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));

// Parse JSON bodies
app.use(express.json());

// Timestamp oluşturmak için yardımcı fonksiyon
function getTimestamp() {
    return Date.now();
}

// HMAC imzası oluşturmak için yardımcı fonksiyon
function createSignature(queryString, apiSecret) {
    return crypto.createHmac('sha256', apiSecret).update(queryString).digest('hex');
}

// Türkiye saatini almak için helper function
const getTurkeyTime = () => {
    return new Date().toLocaleString('sv-SE', {
        timeZone: 'Europe/Istanbul',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).replace(' ', 'T') + '.000Z';
};

// Ortam değişkenine göre statik dosya yolu belirle
const isProduction = process.env.NODE_ENV === 'production';
const staticPath = isProduction ? '/var/www/html' : path.join(__dirname, 'dist');
console.log(`📁 Statik dosya yolu: ${staticPath}`);

if (fs.existsSync(staticPath)) {
    // MIME type'ları düzelt
    app.use(express.static(staticPath, {
        setHeaders: (res, path) => {
            if (path.endsWith('.js')) {
                res.setHeader('Content-Type', 'application/javascript');
            } else if (path.endsWith('.mjs')) {
                res.setHeader('Content-Type', 'application/javascript');
            } else if (path.endsWith('.css')) {
                res.setHeader('Content-Type', 'text/css');
            } else if (path.endsWith('.html')) {
                res.setHeader('Content-Type', 'text/html');
            } else if (path.endsWith('.json')) {
                res.setHeader('Content-Type', 'application/json');
            }
        }
    }));
    console.log('✅ Statik dosya servisi aktif (MIME types düzeltildi)');
} else {
    console.warn(`⚠️ Statik dosya yolu bulunamadı: ${staticPath}`);
}

app.use((req, res, next) => {
    const turkeyTime = getTurkeyTime();
    console.log(`${turkeyTime} - ${req.method} ${req.path} - IP: ${req.ip}`);
    next();
});

app.get('/health', (req, res) => {
    const turkeyTime = getTurkeyTime();
    res.json({
        status: 'healthy',
        serverIP: '************',
        timestamp: turkeyTime,
        utcTimestamp: new Date().toISOString(),
        nodeVersion: process.version,
        environment: process.env.NODE_ENV || 'development',
    });
});

app.get('/api/status', (req, res) => {
    const turkeyTime = getTurkeyTime();
    res.json({
        server: 'Crypto Future Streamer API Proxy',
        status: 'running',
        timestamp: turkeyTime
    });
});

// Authentication middleware
const requireAuth = (req, res, next) => {
    const turkeyTime = getTurkeyTime();

    // 🔧 DÜZELTME: Detaylı session debug
    console.log(`${turkeyTime} - 🔍 Session Debug for ${req.path}:`, {
        hasSession: !!req.session,
        hasUser: !!req.session?.user,
        sessionId: req.session?.id || 'none',
        user: req.session?.user || 'none',
        hasApiKeys: !!req.session?.apiKeys,
        cookies: req.headers.cookie ? 'present' : 'none'
    });

    if (!req.session.user) {
        console.log(`${turkeyTime} - ❌ Unauthorized access attempt to ${req.path} - No session user`);
        return res.status(401).json({
            error: 'Authentication required',
            message: 'Please login first',
            timestamp: turkeyTime,
            debug: {
                hasSession: !!req.session,
                sessionId: req.session?.id || 'none'
            }
        });
    }
    console.log(`${turkeyTime} - ✅ Authenticated user: ${req.session.user.username}`);
    next();
};

// Login endpoint
app.post('/api/login', (req, res) => {
    const turkeyTime = getTurkeyTime();
    const { username, password } = req.body;

    console.log(`${turkeyTime} - 🔐 Login attempt for user: ${username}`);

    if (!username || !password) {
        return res.status(400).json({
            error: 'Username and password are required',
            timestamp: turkeyTime
        });
    }

    // Find user in USERS array
    const user = USERS.find(u => u.username === username && u.password === password);

    if (!user) {
        console.log(`${turkeyTime} - ❌ Invalid credentials for user: ${username}`);
        return res.status(401).json({
            error: 'Invalid username or password',
            timestamp: turkeyTime
        });
    }

    // Get API keys for this user
    const userApiKeys = API_KEYS[username];
    if (!userApiKeys) {
        console.log(`${turkeyTime} - ❌ No API keys found for user: ${username}`);
        return res.status(500).json({
            error: 'User configuration error',
            message: 'API keys not found for user',
            timestamp: turkeyTime
        });
    }

    // Store user session
    req.session.user = {
        username: user.username,
        displayName: user.displayName,
        role: user.role
    };

    // Store API keys in session (never send to frontend)
    req.session.apiKeys = userApiKeys;

    console.log(`${turkeyTime} - ✅ Login successful for user: ${username} (${user.displayName})`);

    res.json({
        success: true,
        user: {
            username: user.username,
            displayName: user.displayName,
            role: user.role
        },
        message: 'Login successful',
        timestamp: turkeyTime
    });
});

// Logout endpoint
app.post('/api/logout', (req, res) => {
    const turkeyTime = getTurkeyTime();
    const username = req.session.user?.username || 'unknown';

    console.log(`${turkeyTime} - 🚪 Logout request from user: ${username}`);

    // Clear session
    req.session.destroy((err) => {
        if (err) {
            console.error(`${turkeyTime} - ❌ Error destroying session:`, err);
            return res.status(500).json({
                error: 'Logout failed',
                message: 'Could not clear session',
                timestamp: turkeyTime
            });
        }

        console.log(`${turkeyTime} - ✅ Logout successful for user: ${username}`);
        res.json({
            success: true,
            message: 'Logout successful',
            timestamp: turkeyTime
        });
    });
});

// Credentials endpoint - get user credentials
app.get('/api/credentials/:username', requireAuth, (req, res) => {
    const turkeyTime = getTurkeyTime();
    const { username } = req.params;

    // Verify user is requesting their own credentials
    if (req.session.user.username !== username) {
        console.log(`${turkeyTime} - ❌ Unauthorized credentials request: ${req.session.user.username} requested ${username}`);
        return res.status(403).json({
            error: 'Unauthorized',
            message: 'You can only access your own credentials',
            timestamp: turkeyTime
        });
    }

    // Get credentials from session
    const userCredentials = req.session.apiKeys;
    if (!userCredentials) {
        console.log(`${turkeyTime} - ❌ No credentials found in session for user: ${username}`);
        return res.status(404).json({
            error: 'Credentials not found',
            message: 'No API keys found for user',
            timestamp: turkeyTime
        });
    }

    console.log(`${turkeyTime} - ✅ Credentials retrieved for user: ${username}`);

    // Return credentials (safe to send since it's over HTTPS and to authenticated user)
    res.json({
        apiKey: userCredentials.apiKey,
        apiSecret: userCredentials.apiSecret,
        ed25519ApiKey: userCredentials.ed25519ApiKey,
        privateKeyHex: userCredentials.privateKeyHex
    });
});

// HMAC SHA256 imzalama fonksiyonu
function generateHmacSignature(messageString, secret) {
    return crypto.createHmac('sha256', secret).update(messageString).digest('hex');
}

// Note: Removed unused credential helper functions
// All credential management is now handled through API_KEYS object

// Binance API Proxy endpoint
app.post('/api/binance/proxy', requireAuth, async (req, res) => {
    const turkeyTime = getTurkeyTime();
    console.log(`${turkeyTime} - 🎉 /api/binance/proxy POST isteği alındı. User: ${req.session.user.username}, Body:`, req.body);

    try {
        const { endpoint, method = 'GET', headers: clientHeaders = {}, body: requestBody = null, params: clientParams = {} } = req.body;

        if (!endpoint) {
            return res.status(400).json({ error: 'Endpoint is required', timestamp: turkeyTime });
        }

        // Use session-based credentials
        const userCredentials = req.session.apiKeys;
        if (!userCredentials) {
            return res.status(401).json({
                error: 'User credentials not found in session',
                message: 'Please login again',
                timestamp: turkeyTime
            });
        }
        console.log(`${turkeyTime} - 🔑 Using session credentials for user: ${req.session.user.username}`);

        const binanceBaseUrl = "https://fapi.binance.com";
        let finalUrl = `${binanceBaseUrl}${endpoint}`;
        const requestOptions = {
            method,
            headers: {
                'User-Agent': 'crypto-future-streamer-server/1.0'
            }
        };

        // Body varsa ekle
        if (requestBody && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            if (typeof requestBody === 'string') {
                requestOptions.body = requestBody;
                if (!requestOptions.headers['Content-Type']) requestOptions.headers['Content-Type'] = 'application/x-www-form-urlencoded';
            } else if (typeof requestBody === 'object') {
                requestOptions.body = JSON.stringify(requestBody);
                if (!requestOptions.headers['Content-Type']) requestOptions.headers['Content-Type'] = 'application/json';
            }
        }

        // Server'da doğru timestamp oluştur
        let timestamp = Date.now();
        console.log(`${turkeyTime} - 🕐 Generated timestamp: ${timestamp}, Date: ${new Date(timestamp).toISOString()}`);

        // Parametreleri birleştir
        let allParams = { ...req.body.params };
        allParams.timestamp = timestamp;

        // Query string oluştur
        let queryString = Object.keys(allParams)
            .sort()
            .map(key => `${key}=${allParams[key]}`)
            .join('&');

        // İmza gerektiren endpoint'ler için HMAC imzalama
        if (endpoint === '/fapi/v2/account' || endpoint === '/fapi/v2/positionRisk' || endpoint === '/fapi/v1/userTrades' ||
            endpoint === '/fapi/v1/openOrders' || endpoint === '/fapi/v1/allOpenOrders') {
            const signature = generateHmacSignature(queryString, userCredentials.apiSecret);
            finalUrl = `${finalUrl}?${queryString}&signature=${signature}`;
            requestOptions.headers['X-MBX-APIKEY'] = userCredentials.apiKey;
            console.log(`${turkeyTime} - 🔑 HMAC imza oluşturuldu. Query: ${queryString}, Signature (hex): ${signature.substring(0, 16)}...`);
        }
        // Tüm imzalı endpoint'ler için HMAC-SHA256 kullan
        else if (clientHeaders['X-MBX-APIKEY'] || endpoint.includes('/fapi/v1/order') || endpoint.includes('/fapi/v1/batchOrders')) {
            let queryParams = {
                timestamp,
                ...clientParams
            };

            // Query string'i parametreleri alfabetik olarak sıralayarak oluştur
            queryString = Object.keys(queryParams)
                .sort()
                .filter(key => queryParams[key] !== undefined)
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
                .join('&');

            // HMAC-SHA256 imzası oluştur
            const signature = generateHmacSignature(queryString, userCredentials.apiSecret);
            finalUrl = `${binanceBaseUrl}${endpoint}?${queryString}&signature=${signature}`;
            requestOptions.headers['X-MBX-APIKEY'] = userCredentials.apiKey;
            console.log(`${turkeyTime} - 🔑 HMAC-SHA256 imza oluşturuldu. Query: ${queryString}, Signature (hex): ${signature.substring(0, 16)}...`);
        }
        // İmzasız endpoint'ler için (listenKey gibi) - sadece API key gerekli
        else if (endpoint.includes('/listenKey')) {
            requestOptions.headers['X-MBX-APIKEY'] = userCredentials.apiKey;
            console.log(`${turkeyTime} - 🔑 API key eklendi (imzasız endpoint): ${endpoint}`);
        }

        console.log(`${turkeyTime} - 🚀 Sunucu taraflı Binance API İsteği: ${method} ${finalUrl}`);
        console.log(`${turkeyTime} - 📤 Request options:`, JSON.stringify(requestOptions, null, 2));

        const response = await fetch(finalUrl, requestOptions);

        console.log(`${turkeyTime} - 📊 Binance API Yanıt Durumu: ${response.status}`);
        const responseText = await response.text();
        let responseData;

        try {
            responseData = JSON.parse(responseText);
        } catch (parseError) {
            console.warn(`${turkeyTime} - ⚠️ Binance API yanıtı JSON parse edilemedi. Yanıt:`, responseText.substring(0, 200));
            if (!response.ok) {
                return res.status(response.status).json({
                    status: response.status,
                    result: { error: 'Binance API returned non-JSON error', details: responseText },
                    success: false,
                    headers: Object.fromEntries(response.headers.entries()),
                    timestamp: turkeyTime
                });
            }
            responseData = { raw_text: responseText };
        }
        console.log(`${turkeyTime} - 📊 Binance API Yanıt Verisi:`, responseData);

        res.status(response.status).json({
            status: response.status,
            result: responseData,
            success: response.ok,
            headers: Object.fromEntries(response.headers.entries()),
            timestamp: turkeyTime
        });

    } catch (error) {
        console.error(`${turkeyTime} - ❌ Server-side Binance API Proxy Hatası:`, error);
        res.status(500).json({
            error: 'Server-side proxy error',
            message: error.message,
            success: false,
            timestamp: turkeyTime
        });
    }
});

// Binance Futures User Data Stream - Create Listen Key
app.post('/api/binance/fapi/v1/listenKey', requireAuth, async (req, res) => {
    const turkeyTime = getTurkeyTime();
    try {
        const userCredentials = req.session.apiKeys;
        if (!userCredentials) {
            return res.status(401).json({
                error: 'User credentials not found in session',
                timestamp: turkeyTime
            });
        }

        console.log(`${turkeyTime} - 🔑 Creating listen key for user: ${req.session.user.username}`);

        const response = await fetch(`https://fapi.binance.com/fapi/v1/listenKey`, {
            method: 'POST',
            headers: {
                'X-MBX-APIKEY': userCredentials.apiKey,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (response.ok) {
            console.log(`${turkeyTime} - ✅ Listen Key oluşturuldu for user: ${req.session.user.username}`);
            res.json(data);
        } else {
            console.error(`${turkeyTime} - ❌ Listen Key oluşturma hatası:`, data);
            res.status(response.status).json(data);
        }
    } catch (error) {
        console.error(`${turkeyTime} - ❌ Listen Key API hatası:`, error);
        res.status(500).json({ error: 'Internal server error', timestamp: turkeyTime });
    }
});

// Binance Futures User Data Stream - Keep Alive Listen Key
app.put('/api/binance/fapi/v1/listenKey', requireAuth, async (req, res) => {
    const turkeyTime = getTurkeyTime();
    try {
        const userCredentials = req.session.apiKeys;
        const { listenKey } = req.body;

        if (!userCredentials) {
            return res.status(401).json({
                error: 'User credentials not found in session',
                timestamp: turkeyTime
            });
        }

        if (!listenKey) {
            return res.status(400).json({
                error: 'Listen key required',
                timestamp: turkeyTime
            });
        }

        const response = await fetch(`https://fapi.binance.com/fapi/v1/listenKey`, {
            method: 'PUT',
            headers: {
                'X-MBX-APIKEY': userCredentials.apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ listenKey })
        });

        if (response.ok) {
            console.log(`${turkeyTime} - ✅ Listen Key yenilendi for user: ${req.session.user.username}`);
            res.json({ success: true });
        } else {
            const data = await response.json();
            console.error(`${turkeyTime} - ❌ Listen Key yenileme hatası:`, data);
            res.status(response.status).json(data);
        }
    } catch (error) {
        console.error(`${turkeyTime} - ❌ Listen Key yenileme API hatası:`, error);
        res.status(500).json({ error: 'Internal server error', timestamp: turkeyTime });
    }
});

// Binance Futures User Data Stream - Delete Listen Key
app.delete('/api/binance/fapi/v1/listenKey', requireAuth, async (req, res) => {
    const turkeyTime = getTurkeyTime();
    try {
        const userCredentials = req.session.apiKeys;
        const { listenKey } = req.body;

        if (!userCredentials) {
            return res.status(401).json({
                error: 'User credentials not found in session',
                timestamp: turkeyTime
            });
        }

        if (!listenKey) {
            return res.status(400).json({
                error: 'Listen key required',
                timestamp: turkeyTime
            });
        }

        const response = await fetch(`https://fapi.binance.com/fapi/v1/listenKey`, {
            method: 'DELETE',
            headers: {
                'X-MBX-APIKEY': userCredentials.apiKey,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ listenKey })
        });

        if (response.ok) {
            console.log(`${turkeyTime} - ✅ Listen Key silindi for user: ${req.session.user.username}`);
            res.json({ success: true });
        } else {
            const data = await response.json();
            console.error(`${turkeyTime} - ❌ Listen Key silme hatası:`, data);
            res.status(response.status).json(data);
        }
    } catch (error) {
        console.error(`${turkeyTime} - ❌ Listen Key silme API hatası:`, error);
        res.status(500).json({ error: 'Internal server error', timestamp: turkeyTime });
    }
});

// Binance Futures V2 Account Information
app.get('/api/binance/fapi/v2/account', requireAuth, async (req, res) => {
    const turkeyTime = getTurkeyTime();
    try {
        const userCredentials = req.session.apiKeys;
        if (!userCredentials) {
            return res.status(401).json({
                error: 'User credentials not found in session',
                timestamp: turkeyTime
            });
        }

        const timestamp = getTimestamp();
        const queryString = `timestamp=${timestamp}`;
        const signature = createSignature(queryString, userCredentials.apiSecret);

        console.log(`${turkeyTime} - 🔑 Making account request for user: ${req.session.user.username}`, {
            url: `${BINANCE_FUTURES_BASE_URL}/fapi/v2/account`,
            hasApiKey: !!userCredentials.apiKey,
            hasSignature: !!signature,
            timestamp
        });

        const response = await fetch(`${BINANCE_FUTURES_BASE_URL}/fapi/v2/account?${queryString}&signature=${signature}`, {
            method: 'GET',
            headers: {
                'X-MBX-APIKEY': userCredentials.apiKey,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        const data = await response.json();

        if (!response.ok) {
            console.error(`${turkeyTime} - Binance API error for user ${req.session.user.username}:`, {
                status: response.status,
                statusText: response.statusText,
                data
            });
            return res.status(response.status).json(data);
        }

        console.log(`${turkeyTime} - ✅ Account V2 data retrieved successfully for user: ${req.session.user.username}`);
        console.log(`${turkeyTime} - 📊 Account summary:`, {
            totalWalletBalance: data.totalWalletBalance,
            availableBalance: data.availableBalance,
            assetsCount: data.assets?.length || 0,
            positionsCount: data.positions?.length || 0
        });

        res.json(data);
    } catch (error) {
        console.error(`${turkeyTime} - Account V2 endpoint error:`, error);
        res.status(500).json({
            error: 'Server error',
            message: error.message,
            timestamp: turkeyTime
        });
    }
});

app.get('*', (req, res) => {
    const turkeyTime = getTurkeyTime();

    // API endpoint'leri için 404 döndür
    if (req.path.startsWith('/api/') || req.path.startsWith('/health')) {
        return res.status(404).json({ error: 'API endpoint not found', path: req.path, timestamp: getTurkeyTime() });
    }

    // Static dosyalar için doğru MIME type ile serve et
    if (req.path.endsWith('.js') || req.path.endsWith('.mjs')) {
        const filePath = path.join(staticPath, req.path);
        if (fs.existsSync(filePath)) {
            res.setHeader('Content-Type', 'application/javascript');
            return res.sendFile(filePath);
        }
    }

    if (req.path.endsWith('.css')) {
        const filePath = path.join(staticPath, req.path);
        if (fs.existsSync(filePath)) {
            res.setHeader('Content-Type', 'text/css');
            return res.sendFile(filePath);
        }
    }

    // Diğer tüm istekler için index.html döndür (SPA routing)
    const indexPath = path.join(staticPath, 'index.html');
    if (fs.existsSync(indexPath)) {
        res.setHeader('Content-Type', 'text/html');
        res.sendFile(indexPath);
    } else {
        res.status(404).json({ error: 'Frontend not found', message: `index.html not found at ${indexPath}` });
    }
});

app.use((error, req, res, next) => {
    const turkeyTime = getTurkeyTime();
    console.error(`${turkeyTime} - 🔥 Unhandled server error:`, error);
    res.status(500).json({
        error: 'Internal server error',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
});

const privateKeyPath = '/etc/letsencrypt/live/parabot.fun/privkey.pem';
const certificatePath = '/etc/letsencrypt/live/parabot.fun/fullchain.pem';

let privateKeySsl;
let certificateSsl;
let isHttps = false;

try {
    privateKeySsl = fs.readFileSync(privateKeyPath, 'utf8');
    certificateSsl = fs.readFileSync(certificatePath, 'utf8');
    console.log('✅ SSL sertifika dosyaları başarıyla okundu.');
    isHttps = true;
} catch (err) {
    console.log(`🔶 SSL sertifika dosyaları bulunamadı: ${err.message}`);
    console.log('🔶 HTTP modunda devam ediliyor (SSL olmadan)');
}

// SSL varsa HTTPS, yoksa HTTP sunucusu başlat
if (isHttps) {
    const httpsOptions = {
        key: privateKeySsl,
        cert: certificateSsl
    };

    https.createServer(httpsOptions, app).listen(PORT, () => {
        console.log(`🚀 Crypto Future Streamer API Proxy Server başlatıldı: https://localhost:${PORT} -> https://parabot.fun`);
    });
} else {
    // HTTP sunucusu başlat
    app.listen(PORT, () => {
        console.log(`🚀 Crypto Future Streamer API Proxy Server başlatıldı: http://localhost:${PORT} (HTTP modunda)`);
    });
}

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    process.exit(0);
});