import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useWebSocket } from "@/hooks/useWebSocket";
import useMovingAverage from "@/hooks/useMovingAverage";
import { ConfigSettings } from "@/components/settings/ConfigPanel";
import ConfigPanel from "@/components/settings/ConfigPanel";
import MarketData from "@/components/dashboard/MarketData";
import TradingPanel from "@/components/trading/TradingPanel";
import TechnicalSignals from "@/components/trading/TechnicalSignals";
import TradesList from "@/components/trading/TradesList";
import AiTradeOptimizer from "@/components/trading/AiTradeOptimizer";
import AutoTradingLogic from "@/components/trading/AutoTradingLogic";
import UnifiedTradingSettings from "@/components/trading/UnifiedTradingSettings";
import AdvancedTradingConfig from "@/components/trading/AdvancedTradingConfig";
import type { AdvancedTradingConfig as ServiceAdvancedTradingConfig } from '@/services/advancedTradingService';
import type { AdvancedTradingConfig as TypesAdvancedTradingConfig } from '@/types/advancedTrading';
import { DEFAULT_ADVANCED_CONFIG } from '@/types/advancedTrading';
import useFundingRate from '@/hooks/useFundingRate';
import DashboardHeader from '@/components/layout/DashboardHeader';
import { RealTradingPanel } from '@/components/RealTradingPanel'; // Import RealTradingPanel
import { canOpenTrade, checkRateLimit, monitorTradePrice, calculateTradeQuantity, loadExchangeInfoToRAM, isExchangeInfoReady, getLoadedSymbolCount } from '@/utils/tradingCalculations';
import useBinanceData from '@/hooks/useBinanceData';
import binanceApi from '@/services/binanceApi';
// TradeMonitor'den gelen AccountBalance tipiyle uyumlu olması için özel bir interface tanımlıyoruz
interface MonitorAccountBalance {
    asset: string;
    free: string;
    locked: string;
    walletBalance?: string;
    crossWalletBalance?: string;
    crossUnPnl?: string;
    availableBalance?: string;
    marginBalance?: string;
    positionInitialMargin?: string;
    totalPositionInitialMargin?: string;
    unrealizedProfit?: string;
}
import { useBinanceAccount } from '@/hooks/useBinanceAccount';
import CoinCard from "@/components/dashboard/CoinCard";
import AccountInfo from "@/components/dashboard/AccountInfo";
import TradeHistory from "@/components/trading/TradeHistory";
import { Trade, TradeStats } from '@/types/trading'; // Import Trade and TradeStats interfaces
// TradeMonitor'den güncellenmiş işlem tipi
interface TradeUpdate extends Trade {
}
import TradeMonitor from "@/components/trading/TradeMonitor";
import useRealTrading from "@/hooks/useRealTrading"; // useRealTrading hook'unu import ediyoruz
import { BinanceOrderResponse } from '@/services/binanceService'; // BinanceOrderResponse tipini import ediyoruz
import { useAuth } from '@/contexts/AuthContext'; // Dynamic credentials
import {
    initAdvancedTradingService,
    shutdownAdvancedTradingService
} from '@/services/advancedTradingService';
import {
    initializeTradingService,
    shutdownTradingService
} from '@/services/tradingService';
import AdvancedTradingManager from '@/services/advancedTradingManager';

interface CoinCardData {
    symbol: string;
    price: number;
    priceChangePercent: number;
    volume: number;
    isAlerted?: boolean;
    lastTradeTime?: Date;
    isTopGainer?: boolean;
}

// Define TradingRecommendations interface
interface TradingRecommendations {
    tradingDirection: 'long' | 'short';
    profitTarget: number;
    stopLoss: number;
    tradeUnit: number;
    leverage: number;
    reason: string;
}

const Dashboard = () => {
    const { toast } = useToast();

    // State for connection and balance management
    const [isConnected, setIsConnected] = useState(false);
    const [connectionStatus, setConnectionStatus] = useState('disconnected');
    const [accountBalance, setAccountBalance] = useState(0);
    const [initialBalance, setInitialBalance] = useState(0);
    const [isRealTrading, setIsRealTrading] = useState(true);

    // Get current user and API keys from AuthContext
    const { currentUser, currentUserApiKeys } = useAuth();

    // Debug credentials
    useEffect(() => {
        console.log('🔍 [Dashboard] Credentials Debug:', {
            hasCurrentUserApiKeys: !!currentUserApiKeys,
            currentUser: currentUser?.username || 'none',
            apiKey: currentUserApiKeys?.apiKey ? currentUserApiKeys.apiKey.substring(0, 8) + '...' : 'none',
            apiSecret: currentUserApiKeys?.apiSecret ? 'present' : 'none'
        });
    }, [currentUserApiKeys, currentUser]);

    const {
        accountInfo,
        isLoading: isLoadingAccountInfo,
        fetchAccountInfo,
        setLeverage: setAccountLeverage
    } = useBinanceAccount();

    // 🔧 DÜZELTME: Credentials hazır olduğunda bakiye yükle (ayrı useEffect)
    // CIRCULAR DEPENDENCY SORUNU NEDENIYLE KALDIRILDI
    // useEffect(() => {
    //     if (currentUserApiKeys?.apiKey && currentUserApiKeys?.apiSecret && !initialBalanceLoaded) {
    //         console.log('🔄 [Dashboard] Credentials ready, fetching account info...');
    //         const timer = setTimeout(() => {
    //             fetchAccountInfo();
    //         }, 500); // 500ms gecikme ile

    //         return () => clearTimeout(timer);
    //     }
    // }, [currentUserApiKeys?.apiKey, currentUserApiKeys?.apiSecret, initialBalanceLoaded]);

    // Add useFundingRate and useBinanceData hooks
    const {
        fundingRates,
        isLoading: isLoadingFundingRates,
        hasNegativeFunding,
        isNearFundingTime,
        minutesUntilNextFunding
    } = useFundingRate();

    // Enhanced Binance data hook with improved trade monitoring
    const {
        newlyListedCoins: newCoinsMap,
        // monitorTradePrice, // This is already imported from tradingCalculations, avoid conflict
        subscribeToSymbolUpdates
    } = useBinanceData(false); // Her zaman gerçek mod kullan

    // soundEnabled removed - not needed without DashboardHeader
    const [autoTradingEnabled, setAutoTradingEnabled] = useState<boolean>(false);
    const [tradingDirection, setTradingDirection] = useState<'long' | 'short'>('short');
    const [tradeUnit, setTradeUnit] = useState<number>(0.5);
    const [profitTarget, setProfitTarget] = useState<number>(0.5); // Default kar hedefi 0.3%
    const [stopLoss, setStopLoss] = useState<number>(0.5); // Default stop loss 0.3%
    const [tradingStatus, setTradingStatus] = useState<'inactive' | 'connecting' | 'active' | 'error'>('inactive');
    const [selectedSymbols, setSelectedSymbols] = useState<string[]>(['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT']);
    const [trades, setTrades] = useState<Trade[]>([]);
    const [alertedCoins, setAlertedCoins] = useState<Set<string>>(new Set());
    const [lastTradeTimeMap, setLastTradeTimeMap] = useState<Record<string, Date>>({
        'BTCUSDT': new Date(),
        'ETHUSDT': new Date(Date.now() - 5 * 60 * 1000)
    });

    // 🔒 MUM BAZLI KONTROL: Aynı mumda işlem açılmasını engelle
    const [lastCandleTradeMap, setLastCandleTradeMap] = useState<Record<string, number>>({});

    // 🔒 GLOBAL TRADE LOCK: Aynı anda birden fazla işlem açılmasını engelle
    const tradeLockRef = useRef<Set<string>>(new Set());

    // Helper function to remove trade lock
    const removeTradeLock = (symbol: string, reason: string) => {
        if (tradeLockRef.current.has(symbol)) {
            tradeLockRef.current.delete(symbol);
            console.log(`🔓 TRADE LOCK REMOVED: ${symbol} için işlem kilidi kaldırıldı (${reason})`);
        }
    };
    const [tradeStats, setTradeStats] = useState<TradeStats>({
        totalTrades: 0,
        profitableTrades: 0,
        lossTrades: 0,
        dailyChangePercent: 0,
        totalProfitAmount: 0,
        totalFees: 0, // Added missing property
        totalNetProfit: 0 // Added missing property
    });
    const [leverage, setLeverage] = useState<number>(20);
    // currentTime removed - not needed without DashboardHeader
    const [autoTradingStartTime, setAutoTradingStartTime] = useState<Date | null>(null);
    const [tradingDuration, setTradingDuration] = useState<string>("00:00:00");
    const [selectedTradingSymbol, setSelectedTradingSymbol] = useState<string>("BTCUSDT");
    const [aiOptimizationEnabled, setAiOptimizationEnabled] = useState<boolean>(false);
    const [lastAiAnalysisTime, setLastAiAnalysisTime] = useState<Date | null>(null);
    const [analysisInterval, setAnalysisInterval] = useState<number>(15);
    const [cumulativeProfitLoss, setCumulativeProfitLoss] = useState<number>(0);
    const [isTotalLossExceeded, setIsTotalLossExceeded] = useState<boolean>(false);
    const [configSettings, setConfigSettings] = useState<ConfigSettings>({
        movingAveragePoints: 10,
        alertPercentage: 0.8, // Default eşik değeri 0.5%
        aiAnalysisInterval: 15,
        maxOpenPositions: 3,
        // Trailing stop ayarları
        // Add missing security settings with safe defaults
        emergencyStopLoss: 10,
        minVolumeForTrade: 5
    });
    const [tradingRestrictions, setTradingRestrictions] = useState({
        restrictNewCoins: true,
        maxTotalLossPercent: 20,
        avoidNegativeFundingForShort: true,
        fundingTimeThresholdMinutes: 30,
        maxDailyTrades: 100, // Keep this for internal logic but don't pass to canOpenTrade
        minTradeInterval: 60 // 🔧 DÜZELTME: 60 saniye (1 dakika) - daha esnek rate limit
    });

    // YENİ: Kriterleri sağlayan coinleri takip edecek state
    const [criteriaEligibleCoins, setCriteriaEligibleCoins] = useState<Set<string>>(new Set());
    const [lastCriteriaCheck, setLastCriteriaCheck] = useState<Record<string, {
        timestamp: number;
        signal: 'long' | 'short' | null;
        confidence: number;
        reasons: string[];
        blockingReasons: string[];
        canTrade: boolean;
    }>>({});

    const { status: _tickerStatus, data: tickerData } = useWebSocket("wss://fstream.binance.com/ws/!ticker@arr");
    const { status: klineStatus, data: klineData } = useWebSocket("wss://fstream.binance.com/ws/!miniTicker@arr");

    const { movingAverages, addPrice, initializeSymbols, isInitialized } = useMovingAverage(configSettings.movingAveragePoints);

    // Add a ref to track rapid price movements
    const priceUpdateTimestampsRef = React.useRef<Record<string, number[]>>({});
    const [symbolsInitialized, setSymbolsInitialized] = useState(false);

    const [stopNewTrades, setStopNewTrades] = useState<boolean>(false);
    // Add state for monitoring high volatility trades with fast price movements
    const [volatilityMonitoringEnabled, setVolatilityMonitoringEnabled] = useState<boolean>(true);
    // Polling frekansını WebSocket kullanımı için daha az agresif yapıyoruz - 250ms çok yüksek CPU kullanabilir
    const [monitoringFrequency, setMonitoringFrequency] = useState<number>(500); // 500ms'de bir kontrol (daha sık kontrol)

    // Initial balance loading state
    const [isLoadingInitialBalance, setIsLoadingInitialBalance] = useState(false);
    const [initialBalanceLoaded, setInitialBalanceLoaded] = useState(false);

    // 💰 CRITICAL FIX: Trading başlatıldığında set edilen initial balance
    const [tradingStartInitialBalance, setTradingStartInitialBalance] = useState<number | null>(null);

    // Advanced Trading states
    const [useAdvancedTrading, setUseAdvancedTrading] = useState<boolean>(false);
    const [advancedTradingConfig, setAdvancedTradingConfig] = useState<ServiceAdvancedTradingConfig>({
        tradeAmount: 0.5, // Default trade amount
        leverage: 20,
        takeProfitPercent: 0.5,
        stopLossPercent: 0.5,
        maxOpenPositions: 3,
        minVolumeForTrade: 5
    });
    const [advancedTradingInitialized, setAdvancedTradingInitialized] = useState(false);

    // New Advanced Trading Manager states
    const [useNewAdvancedFeatures, setUseNewAdvancedFeatures] = useState<boolean>(false);
    const [newAdvancedConfig, setNewAdvancedConfig] = useState<TypesAdvancedTradingConfig>(DEFAULT_ADVANCED_CONFIG);
    const [advancedTradingManager, setAdvancedTradingManager] = useState<AdvancedTradingManager | null>(null);
    const [lastAdvancedAnalysis, setLastAdvancedAnalysis] = useState<Record<string, any>>({});

    // Real trading hook'unu kullanıma hazır hale getiriyoruz
    const {
        placeRealTrade,
        closeRealTrade,
        refreshPositions,
        isPlacingOrder,
        positions
    } = useRealTrading({
        credentials: {
            apiKey: currentUserApiKeys?.apiKey || '',
            apiSecret: currentUserApiKeys?.apiSecret || ''
        },
        leverage: leverage,
        maxOpenPositions: configSettings.maxOpenPositions,
        minVolumeForTrade: configSettings.minVolumeForTrade,
        emergencyStopLoss: configSettings.emergencyStopLoss,
        maxLeverage: 125, // Maksimum kaldıraç (exchange'e göre değişir)
        restrictLowVolumeCoins: true,
        stopLossPercent: stopLoss,
        takeProfitPercent: profitTarget
    });

    // Helper function to get current trade price for a symbol
    const getTradePriceForSymbol = (symbol: string): number => {
        if (!tickerData || !Array.isArray(tickerData)) return 0;

        const ticker = tickerData.find(ticker => ticker.s === symbol);
        if (ticker && ticker.c) {
            return parseFloat(ticker.c);
        }

        return 0;
    };

    // BTCUSDT ve ETHUSDT için verileri al
    const btcUsdtData = React.useMemo(() => {
        if (!tickerData || !Array.isArray(tickerData)) return null;
        const ticker = tickerData.find(t => t.s === 'BTCUSDT');
        return ticker ? {
            symbol: 'BTCUSDT',
            price: parseFloat(ticker.c || '0'),
            priceChangePercent: parseFloat(ticker.P || '0'),
            volume: parseFloat(ticker.q || '0'),
            lastTradeTime: lastTradeTimeMap['BTCUSDT']
        } : null;
    }, [tickerData, lastTradeTimeMap]);

    const ethUsdtData = React.useMemo(() => {
        if (!tickerData || !Array.isArray(tickerData)) return null;
        const ticker = tickerData.find(t => t.s === 'ETHUSDT');
        return ticker ? {
            symbol: 'ETHUSDT',
            price: parseFloat(ticker.c || '0'),
            priceChangePercent: parseFloat(ticker.P || '0'),
            volume: parseFloat(ticker.q || '0'),
            lastTradeTime: lastTradeTimeMap['ETHUSDT']
        } : null;
    }, [tickerData, lastTradeTimeMap]);

    // Düzeltilen checkTradeRestrictions fonksiyonu - Dashboard içindeki tanımını düzgün şekilde yerine taşıyoruz
    const checkTradeRestrictions = useCallback((symbol: string, direction: 'long' | 'short', lastTradeTime?: Date) => {
        // MA200 ve hacim verilerini al
        const ticker = tickerData?.find(t => t.s === symbol);
        const currentPrice = ticker ? parseFloat(ticker.c || '0') : 0;
        const volume24h = ticker ? parseFloat(ticker.q || '0') : 0;
        const priceChangePercent = ticker ? parseFloat(ticker.P || '0') : 0;
        const volumeChangePercent = ticker ? parseFloat(ticker.v || '0') / parseFloat(ticker.v || '1') : 1; // Basit hacim artış oranı

        // MA200 simülasyonu (gerçekte 1H MA200 kullanılmalı)
        const ma200Price = currentPrice * 0.95; // Örnek olarak %5 altında varsayıyoruz

        return canOpenTrade(
            symbol,
            direction,
            configSettings,
            tradingRestrictions,
            lastTradeTime,
            fundingRates
        );
    }, [tickerData, configSettings, tradingRestrictions, fundingRates]); // Added dependencies for useCallback

    // Enhanced closeTrade function with support for additional info
    const closeTrade = async (tradeId: string, additionalInfo?: {
        closeReason?: 'stop_loss' | 'take_profit' | 'emergency_stop_loss' | null,
        profitLossPercent?: number,
        isAutomatic?: boolean,
        fastPriceMovement?: boolean
    }): Promise<void> => {
        const tradeToClose = trades.find(t => t.id === tradeId && t.status === 'open');

        if (!tradeToClose) {
            console.log('❌ Trade not found or already closed:', tradeId);
            return;
        }

        // Sadece gerçek işlem kapanışı
        if (!isConnected) {
            console.warn('❌ WebSocket bağlantısı yok, işlem kapatılamaz');
            toast({
                title: "Bağlantı Hatası",
                description: "WebSocket bağlantısı olmadan işlem kapatılamaz.",
                variant: "destructive",
            });
            return;
        }

        try {
            console.log('🔄 Gerçek işlem kapatılıyor:', tradeToClose);

            // Gerçek API çağrısı yapıyoruz
            const result = await closeRealTrade(tradeToClose);

            if (result.success) {
                console.log('✅ Real trade closed successfully');

                // Update local trade record
                setTrades(prev => prev.map(trade => {
                    if (trade.id === tradeId) {
                        const currentPrice = getTradePriceForSymbol(trade.symbol);
                        const priceChange = trade.direction === 'long'
                            ? (currentPrice - trade.entryPrice) / trade.entryPrice * 100
                            : (trade.entryPrice - currentPrice) / trade.entryPrice * 100;

                        const leveragedProfitLoss = additionalInfo?.profitLossPercent !== undefined
                            ? additionalInfo.profitLossPercent
                            : priceChange * (trade.leverage || 1);

                        const investmentAmount = trade.investmentAmount || 0;
                        const rawProfitAmount = (investmentAmount * leveragedProfitLoss) / 100;

                        const exitAmount = Math.max(0, investmentAmount + rawProfitAmount);
                        const exitCommission = exitAmount * 0.0002; // Real trading commission rate
                        const entryCommission = trade.entryCommission || (investmentAmount * 0.0002);

                        const totalCommission = entryCommission + exitCommission;
                        const fundingFee = 0; // In a real implementation, this would be calculated correctly
                        const totalFees = totalCommission + fundingFee;
                        const actualProfitAmount = rawProfitAmount - totalFees;

                        return {
                            ...trade,
                            status: 'closed' as const,
                            exitPrice: currentPrice,
                            exitTime: new Date(),
                            profitLoss: leveragedProfitLoss,
                            actualProfitAmount: actualProfitAmount,
                            entryCommission: entryCommission,
                            exitCommission: exitCommission,
                            commissionFee: totalCommission,
                            fundingFee: fundingFee,
                            totalFees: totalFees
                        };
                    }
                    return trade;
                }));

                // Show success notification
                toast({
                    title: "Gerçek İşlem Kapatıldı! ✅",
                    description: `${tradeToClose.symbol} gerçek pozisyonu başarıyla kapatıldı.`,
                    variant: "default",
                });

                // İşlem açıldıktan sonra pozisyonları güncelle
                refreshPositions();

                // Update last trade time map
                const symbol = tradeToClose.symbol;
                setLastTradeTimeMap(prev => {
                    const newMap = { ...prev };
                    delete newMap[symbol];
                    return newMap;
                });

            } else {
                console.error('❌ Real trade closure failed:', result);

                toast({
                    title: "Gerçek İşlem Kapatma Hatası! ❌",
                    description: 'Pozisyon kapatılamadı.',
                    variant: "destructive",
                });
            }
        } catch (error: any) {
            console.error('❌ Real trade closure error:', error);

            toast({
                title: "Gerçek İşlem Kapatma Hatası! ❌",
                description: `Hata: ${error.message || 'Bilinmeyen bir hata oluştu.'}`,
                variant: "destructive",
            });
        }
    };

    // Simple close trade wrapper for components that expect single parameter
    const simpleCloseTrade = (tradeId: string) => {
        closeTrade(tradeId);
    };

    // Enhanced trade opening function with improved price monitoring
    const openTradeForSymbol = async (symbol: string, currentPrice: number) => {
        console.log('📊 İşlem açma denemesi:', { symbol, direction: tradingDirection, price: currentPrice });

        // 🚨 ULTRA STRICT DUPLICATE PREVENTION
        const now = Date.now();
        const lastTradeTime = lastTradeTimeMap[symbol]?.getTime() || 0;
        const timeSinceLastTrade = now - lastTradeTime;

        // 🔧 DÜZELTME: Aynı sembol için 1 dakika içinde yeni işlem engelle (daha esnek kontrol)
        const MIN_TRADE_INTERVAL = 1 * 60 * 1000; // 1 dakika = 60000ms
        if (timeSinceLastTrade < MIN_TRADE_INTERVAL) {
            console.log(`🚫 RATE LIMIT: ${symbol} için ${Math.floor(timeSinceLastTrade / 1000)}s önce işlem yapıldı, minimum ${MIN_TRADE_INTERVAL / 1000}s gerekli`);
            toast({
                title: "İşlem Hızı Sınırı",
                description: `${symbol} için çok sık işlem yapılamaz. ${Math.ceil((MIN_TRADE_INTERVAL - timeSinceLastTrade) / 1000)} saniye sonra tekrar deneyin.`,
                variant: "destructive",
            });
            return;
        }

        // 🔒 MUM BAZLI KONTROL: Aynı mumda işlem açılmasını engelle
        const currentCandleTime = Math.floor(now / (60 * 1000)); // 1 dakikalık mum
        const lastCandleTime = lastCandleTradeMap[symbol] || 0;

        if (currentCandleTime === lastCandleTime) {
            console.log(`🚫 CANDLE DUPLICATE PREVENTION: ${symbol} için bu mumda zaten işlem açıldı (Mum: ${currentCandleTime})`);
            toast({
                title: "Aynı Mum Engeli",
                description: `${symbol} için bu mumda zaten işlem açıldı. Yeni mum bekleniyor.`,
                variant: "destructive",
            });
            return;
        }

        // 🔒 IMMEDIATE UPDATE: İşlem açma denemesi başladığında hemen lastTradeTime ve candle time'ı güncelle
        setLastTradeTimeMap(prev => ({
            ...prev,
            [symbol]: new Date(now)
        }));

        setLastCandleTradeMap(prev => ({
            ...prev,
            [symbol]: currentCandleTime
        }));

        // 🔒 GLOBAL TRADE LOCK: Aynı anda birden fazla işlem açılmasını engelle
        if (tradeLockRef.current.has(symbol)) {
            console.log(`🚫 GLOBAL TRADE LOCK: ${symbol} için zaten işlem açılıyor`);
            toast({
                title: "İşlem Kilidi",
                description: `${symbol} için zaten bir işlem açılıyor. Lütfen bekleyin.`,
                variant: "destructive",
            });
            return;
        }

        // Trade lock ekle
        tradeLockRef.current.add(symbol);
        console.log(`🔒 TRADE LOCK ADDED: ${symbol} için işlem kilidi eklendi`);

        // 🚨 CRITICAL: Sadece gerçek Binance pozisyonlarını kullan
        const realOpenPositionsCount = (window as any).realOpenPositionsCount || 0;
        const localOpenTrades = trades.filter(t => t.status === 'open').length;
        const totalOpenPositions = realOpenPositionsCount; // Sadece gerçek pozisyonlar

        console.log(`🔢 POSITION LIMIT CHECK: Gerçek=${realOpenPositionsCount}, Lokal=${localOpenTrades}, Toplam=${totalOpenPositions}, Limit=${configSettings.maxOpenPositions}`);

        if (totalOpenPositions >= configSettings.maxOpenPositions) {
            console.log(`🚫 Cannot open trade: Max positions limit reached (${totalOpenPositions}/${configSettings.maxOpenPositions})`);
            toast({
                title: "Maksimum Pozisyon Limiti",
                description: `${configSettings.maxOpenPositions} açık pozisyon limiti aşıldı. (Gerçek: ${realOpenPositionsCount}, Lokal: ${localOpenTrades})`,
                variant: "destructive",
            });
            removeTradeLock(symbol, "pozisyon limiti");
            return;
        }

        // Aynı coin'e işlem açılmasını önle
        const openPositionSymbols = (window as any).openPositionSymbols || [];
        if (openPositionSymbols.includes(symbol)) {
            console.log(`🚫 DUPLICATE PREVENTION: ${symbol} için zaten açık pozisyon var, yeni işlem açılmıyor`);
            toast({
                title: "Duplicate İşlem Engellendi",
                description: `${symbol} için zaten açık pozisyon bulunuyor.`,
                variant: "destructive",
            });
            removeTradeLock(symbol, "duplicate pozisyon");
            return;
        }

        // Aynı sembol için zaten açık işlem var mı kontrol et (daha detaylı)
        const existingOpenTrade = trades.find(t =>
            t.symbol === symbol &&
            t.status === 'open' &&
            t.direction === tradingDirection
        );

        if (existingOpenTrade) {
            console.log(`🚫 Cannot open trade: Already have an open ${tradingDirection} position for ${symbol}`);
            toast({
                title: "Zaten Açık Pozisyon",
                description: `${symbol} için ${tradingDirection === 'long' ? 'Long' : 'Short'} pozisyon zaten mevcut.`,
                variant: "destructive",
            });
            removeTradeLock(symbol, "zaten açık pozisyon");
            return;
        }

        // Başka yönde açık işlem var mı kontrol et (hedge mode değilse)
        const oppositeDirection = tradingDirection === 'long' ? 'short' : 'long';
        const oppositeOpenTrade = trades.find(t =>
            t.symbol === symbol &&
            t.status === 'open' &&
            t.direction === oppositeDirection
        );

        if (oppositeOpenTrade) {
            console.log(`⚠️ Warning: ${symbol} için ${oppositeDirection} pozisyon mevcut, ${tradingDirection} açılıyor (hedge)`);
            toast({
                title: "Hedge Pozisyon",
                description: `${symbol} için ${oppositeDirection} pozisyon mevcut, ${tradingDirection} hedge pozisyonu açılıyor.`,
                variant: "default",
            });
        }

        // 🔧 DÜZELTME: Daha detaylı bakiye kontrolü ve debug
        console.log(`💰 Bakiye kontrolü: tradeUnit=${tradeUnit}, accountBalance=${accountBalance}, type=${typeof accountBalance}, initialBalanceLoaded=${initialBalanceLoaded}`);

        // Bakiye henüz yüklenmemişse işlem açmayı engelle
        if (accountBalance === 0 && !initialBalanceLoaded) {
            console.log(`⏳ Cannot open trade: Balance not loaded yet - accountBalance=${accountBalance}, initialBalanceLoaded=${initialBalanceLoaded}`);
            toast({
                title: "Bakiye Yükleniyor",
                description: "Hesap bakiyesi henüz yüklenmedi. Lütfen birkaç saniye bekleyin.",
                variant: "default",
            });
            removeTradeLock(symbol, "bakiye yüklenmedi");
            return;
        }

        if (tradeUnit > accountBalance) {
            console.log(`❌ Cannot open trade: Insufficient balance - tradeUnit (${tradeUnit}) > accountBalance (${accountBalance})`);
            toast({
                title: "Yetersiz Bakiye",
                description: `İşlem tutarı (${tradeUnit} USDT) mevcut bakiyeyi (${accountBalance.toFixed(2)} USDT) aşıyor.`,
                variant: "destructive",
            });
            removeTradeLock(symbol, "yetersiz bakiye");
            return;
        }

        console.log(`✅ Bakiye kontrolü geçti: ${tradeUnit} <= ${accountBalance}`);

        // 🔧 DÜZELTME: Minimum işlem değeri kontrolü (daha esnek)
        const minOrderValue = 1; // 5'ten 1'e düşürüldü
        const positionValue = tradeUnit * leverage; // Pozisyon değeri = margin × kaldıraç

        console.log(`📊 Pozisyon değeri kontrolü: positionValue=${positionValue}, minOrderValue=${minOrderValue}`);

        if (positionValue < minOrderValue) {
            const requiredMargin = minOrderValue / leverage; // Gereken margin
            console.log(`❌ Cannot open trade: Position value ${positionValue.toFixed(2)} USDT below minimum ${minOrderValue} USDT (requires ${requiredMargin.toFixed(2)} USDT margin with ${leverage}x leverage)`);
            toast({
                title: "Minimum Pozisyon Değeri",
                description: `Minimum pozisyon değeri ${minOrderValue} USDT olmalıdır. ${leverage}x kaldıraçla minimum ${requiredMargin.toFixed(2)} USDT yatırım gerekli.`,
                variant: "destructive",
            });
            removeTradeLock(symbol, "minimum pozisyon değeri");
            return;
        }

        console.log(`✅ Pozisyon değeri kontrolü geçti: ${positionValue} >= ${minOrderValue}`);

        // Check trading restrictions
        const tradeCheck = checkTradeRestrictions(symbol, tradingDirection);
        if (!tradeCheck.allowed) {
            console.log(`Trade restricted: ${tradeCheck.reason}`);
            toast({
                title: "İşlem Kısıtlı",
                description: tradeCheck.reason,
                variant: "destructive",
            });
            removeTradeLock(symbol, "işlem kısıtlı");
            return;
        }

        // Apply new trading criteria - 1. Alarm Eşik Seviyesi kontrolü
        if (tickerData && Array.isArray(tickerData)) {
            const ticker = tickerData.find(t => t.s === symbol);
            if (!ticker) {
                console.log(`Ticker not found for ${symbol}`);
                removeTradeLock(symbol, "ticker bulunamadı");
                return;
            }

            // MA üzerinde mi kontrolü - İstenen kriterlere uygun
            const maData = movingAverages[symbol];
            if (!maData || !maData.average) {
                console.log(`No MA data for ${symbol}`);
                removeTradeLock(symbol, "MA verisi yok");
                return;
            }

            const priceAboveMA = ((currentPrice - maData.average) / maData.average) * 100;

            // 1. Alarm Eşik Seviyesi (Sinyal Tespiti)
            if (priceAboveMA < configSettings.alertPercentage) {
                console.log(`${symbol}: Trade rejected - Price above MA ${priceAboveMA.toFixed(2)}% below threshold ${configSettings.alertPercentage}%`);
                toast({
                    title: "Sinyal Eşiği",
                    description: `${symbol} için fiyat/MA farkı (%${priceAboveMA.toFixed(2)}) eşiğin altında (%${configSettings.alertPercentage})`,
                    variant: "destructive",
                });
                removeTradeLock(symbol, "sinyal eşiği");
                return;
            }

            // Hacim kontrolü (tüm işlemler için)
            const volume24h = parseFloat(ticker.q || '0');
            const volumeInMillions = volume24h / 1000000;

            // Minimum hacim kontrolü
            if (volumeInMillions < configSettings.minVolumeForTrade) {
                console.log(`${symbol}: Trade rejected - Volume ${volumeInMillions.toFixed(2)}M below threshold ${configSettings.minVolumeForTrade}M`);
                toast({
                    title: "Düşük Hacim",
                    description: `${symbol} 24s hacmi (${volumeInMillions.toFixed(2)}M$) minimum değerin altında (${configSettings.minVolumeForTrade}M$)`,
                    variant: "destructive",
                });
                removeTradeLock(symbol, "düşük hacim");
                return;
            }
        }

        // 2. İşlem Miktarı ve Kaldıraç - ULTRA FAST QUANTITY HESAPLAMA
        // Use the ultra fast RAM-based quantity calculation
        const correctQuantity = calculateTradeQuantity(symbol, currentPrice, tradeUnit, leverage);

        // Calculate total position value (same as positionValue calculated earlier)
        const totalPositionValue = tradeUnit * leverage;

        console.log('📊 İşlem hesaplamaları:', {
            symbol,
            currentPrice,
            tradeUnit, // İşlem miktarı (USDT)
            leverage, // Kaldıraç değeri
            totalPositionValue, // Toplam pozisyon değeri = işlem miktarı × kaldıraç
            correctQuantity // Doğru alım miktarı
        });

        // 3. İşlem Kuralları kontrolü
        try {
            // Local trading rules kullan (network yerine local dosya)
            const { getTradingRulesForSymbol } = await import('../utils/tradingRules');
            const symbolRules = getTradingRulesForSymbol(symbol);

            // Eğer sembol için kural bulunamazsa varsayılan değerleri kullan
            const minOrderQuantity = symbolRules ? parseFloat(symbolRules.contract_size.match(/(\d+\.?\d*)/)?.[1] || '0.001') : 0.001;
            const maxLeverage = symbolRules ? parseInt(symbolRules.leverage || '20') : 20;

            // Miktar kontrolü
            if (correctQuantity < minOrderQuantity) {
                console.log(`${symbol}: Trade rejected - Quantity ${correctQuantity} below min ${minOrderQuantity}`);
                toast({
                    title: "Miktar Hatası",
                    description: `Hesaplanan miktar (${correctQuantity}) minimum değerin altında (${minOrderQuantity})`,
                    variant: "destructive",
                });
                return;
            }

            // Kaldıraç kontrolü
            if (leverage > maxLeverage) {
                console.log(`${symbol}: Warning - Leverage ${leverage}x exceeds max ${maxLeverage}x for this symbol`);
                // Not: Sadece uyarı veriyoruz, işlem engellenmiyor
                toast({
                    title: "Kaldıraç Uyarısı",
                    description: `${symbol} için maksimum kaldıraç ${maxLeverage}x, seçilen kaldıraç ${leverage}x`,
                    variant: "default",
                });
            }
        } catch (error) {
            console.error("Error checking trading rules:", error);
        }

        // 4 ve 5. Kar Marjı ve Stop Loss - Burada sadece hesaplıyoruz
        const adjustedTakeProfit = profitTarget / leverage;
        const adjustedStopLoss = stopLoss / leverage;

        console.log('⚙️ İşlem parametreleri:', {
            karMarjı: `${profitTarget}% (adjusted: ${adjustedTakeProfit.toFixed(4)}%)`,
            stopLoss: `${stopLoss}% (adjusted: ${adjustedStopLoss.toFixed(4)}%)`,
            maxPozisyon: `${configSettings.maxOpenPositions}`,
            currentOpenPositions: `${totalOpenPositions}`,
            realPositions: `${realOpenPositionsCount}`,
            localTrades: `${localOpenTrades}`
        });

        // GERÇEK MOD - Real trading using placeRealTrade hook
        try {
            // 🚨 CRITICAL FIX: Son kez işlem açılabilir mi kontrol et - Sadece gerçek pozisyonlar
            const realOpenPositionsCount = (window as any).realOpenPositionsCount || 0;
            const localOpenTrades = trades.filter(t => t.status === 'open').length;
            const totalOpenPositions = realOpenPositionsCount; // Sadece gerçek pozisyonlar

            if (totalOpenPositions >= configSettings.maxOpenPositions) {
                console.log(`🚫 FINAL POSITION CHECK FAILED: Max positions limit reached (${totalOpenPositions}/${configSettings.maxOpenPositions})`);
                toast({
                    title: "Maksimum Pozisyon Limiti",
                    description: `${configSettings.maxOpenPositions} açık pozisyon limiti aşıldı. (Gerçek: ${realOpenPositionsCount}, Lokal: ${localOpenTrades})`,
                    variant: "destructive",
                });
                removeTradeLock(symbol, "final pozisyon limiti");
                return;
            }

            console.log(`✅ FINAL POSITION CHECK PASSED: ${totalOpenPositions}/${configSettings.maxOpenPositions} - İşlem açılabilir`);

            console.log('🚀 REAL TRADING MODE: Opening real trade');
            console.log('📊 Trade params:', {
                symbol,
                direction: tradingDirection,
                amount: tradeUnit,
                leverage,
                currentPrice,
                calculatedQuantity: correctQuantity,
                takeProfitPercent: adjustedTakeProfit,
                stopLossPercent: adjustedStopLoss
            });

            // Gerçek Binance API çağrısını yapıyoruz
            const orderResult = await placeRealTrade({
                symbol,
                direction: tradingDirection,
                amount: tradeUnit,
                currentPrice,
                volume24h: tickerData?.find(t => t.s === symbol)?.q ? parseFloat(tickerData.find(t => t.s === symbol)!.q) : 0
            });

            if (orderResult.success) {
                console.log('✅ Real order placed successfully:', orderResult);

                // Create trade record for real trade
                const tradeId = `real-trade-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
                const newTrade: Trade = {
                    id: tradeId,
                    symbol,
                    entryPrice: currentPrice,
                    quantity: correctQuantity, // Use correct quantity calculation
                    direction: tradingDirection,
                    openTime: new Date(),
                    status: 'open',
                    leverage: leverage,
                    investmentAmount: tradeUnit,
                    binanceOrderId: orderResult.data ?
                        (typeof orderResult.data.orderId === 'string' ?
                            parseInt(orderResult.data.orderId) :
                            orderResult.data.orderId) :
                        undefined
                };

                setTrades(prev => [...prev, newTrade]);

                toast({
                    title: "🚀 Gerçek İşlem Açıldı",
                    description: `${symbol} için ${tradingDirection === 'long' ? 'Long' : 'Short'} pozisyon açıldı.`,
                    variant: "default",
                });

                // Subscribe to symbol updates
                subscribeToSymbolUpdates(symbol);

                // İşlem açıldıktan sonra pozisyonları güncelle
                refreshPositions();

                // İşlem açıldıktan sonra tekrar açık pozisyon sayısını kontrol et
                const newOpenTradeCount = trades.filter(t => t.status === 'open').length + 1;
                console.log(`✅ İşlem sonrası açık pozisyon sayısı: ${newOpenTradeCount}/${configSettings.maxOpenPositions}`);

                if (newOpenTradeCount > configSettings.maxOpenPositions) {
                    console.warn(`⚠️ WARNING: Max positions limit exceeded after trade! (${newOpenTradeCount}/${configSettings.maxOpenPositions})`);
                }

                removeTradeLock(symbol, "başarılı");
                return; // Exit after successful real trade
            } else {
                // This block should ideally be unreachable if orderResult.success is true
                // If real API returns an error, it should be caught by the catch block
                console.error('❌ Real trade failed (unexpected path):', orderResult);

                toast({
                    title: "Gerçek İşlem Hatası! ❌",
                    description: `Hata: İşlem açılamadı.`,
                    variant: "destructive",
                });

                removeTradeLock(symbol, "başarısız");
                return; // Exit on error
            }

        } catch (error: any) { // Explicitly type error as any
            console.error('❌ Real trade error:', error);

            toast({
                title: "Gerçek İşlem Hatası! ❌",
                description: `Hata: ${error.message || 'Bilinmeyen bir hata oluştu.'}`,
                variant: "destructive",
            });

            removeTradeLock(symbol, "hata");
            return; // Exit on error
        } finally {
            // 🔓 FINAL CLEANUP: Her durumda trade lock'u kaldır
            removeTradeLock(symbol, "finally cleanup");
        }
    };

    // Advanced Trading başlatma/durdurma
    const handleToggleAdvancedTrading = async () => {
        if (!useAdvancedTrading) {
            try {
                console.log('🚀 Advanced Trading başlatılıyor...');

                const credentials = {
                    apiKey: currentUserApiKeys?.apiKey || '',
                    secretKey: currentUserApiKeys?.apiSecret || ''
                };

                const success = await initAdvancedTradingService(credentials, advancedTradingConfig);

                if (success) {
                    setAdvancedTradingInitialized(true);
                    setUseAdvancedTrading(true);

                    toast({
                        title: "Advanced Trading Başlatıldı",
                        description: "ISOLATED mod ve otomatik TP/SL desteği aktif.",
                    });
                } else {
                    toast({
                        title: "Advanced Trading Başlatılamadı",
                        description: "Lütfen API bağlantınızı kontrol edin.",
                        variant: "destructive",
                    });
                }
            } catch (error) {
                console.error('❌ Advanced Trading başlatılırken hata:', error);
                toast({
                    title: "Hata",
                    description: `Advanced Trading başlatılamadı: ${error}`,
                    variant: "destructive",
                });
            }
        } else {
            try {
                console.log('🛑 Advanced Trading durduruluyor...');

                await shutdownAdvancedTradingService();
                setAdvancedTradingInitialized(false);
                setUseAdvancedTrading(false);

                toast({
                    title: "Advanced Trading Durduruldu",
                    description: "Servis güvenli şekilde durduruldu.",
                });
            } catch (error) {
                console.error('❌ Advanced Trading durdurulurken hata:', error);
            }
        }
    };

    const handleToggleAutoTrading = async () => {
        console.log('🔄 [handleToggleAutoTrading] Butona tıklandı:', {
            autoTradingEnabled,
            accountBalance,
            tradeUnit,
            initialBalanceLoaded
        });

        if (!autoTradingEnabled) {
            // 🔧 DÜZELTME: Bakiye kontrolü - otomatik işlem başlatmadan önce
            if (accountBalance === 0 && !initialBalanceLoaded) {
                console.log('❌ [handleToggleAutoTrading] Bakiye yüklenmedi');
                toast({
                    title: "Bakiye Yüklenmedi",
                    description: "Hesap bakiyesi henüz yüklenmedi. Lütfen birkaç saniye bekleyip tekrar deneyin.",
                    variant: "destructive",
                });
                return;
            }

            if (accountBalance < tradeUnit) {
                console.log('❌ [handleToggleAutoTrading] Yetersiz bakiye:', { accountBalance, tradeUnit });
                toast({
                    title: "Yetersiz Bakiye",
                    description: `Otomatik işlem için minimum ${tradeUnit} USDT bakiye gerekli. Mevcut bakiye: ${accountBalance.toFixed(2)} USDT`,
                    variant: "destructive",
                });
                return;
            }

            console.log('✅ [handleToggleAutoTrading] Bakiye kontrolleri geçti, işlem başlatılıyor...');

            // 🚀 ULTRA FAST PRECISION: Exchange info'yu RAM'e yükle
            if (!isExchangeInfoReady()) {
                console.log("⚡ Exchange info RAM'e yükleniyor...");
                const exchangeInfoLoaded = await loadExchangeInfoToRAM();

                if (!exchangeInfoLoaded) {
                    toast({
                        title: "Exchange Info Hatası",
                        description: "Trading kuralları yüklenemedi. Lütfen internet bağlantınızı kontrol edin.",
                        variant: "destructive",
                    });
                    return;
                }

                console.log(`✅ Exchange info yüklendi: ${getLoadedSymbolCount()} symbol precision bilgisi RAM'de`);
            }

            // İşlemi başlatırken tüm MA verilerini sıfırla ve REST API ile yükle
            if (!symbolsInitialized) {
                console.log("🚀 İşlem başlatılıyor: MA verileri REST API ile yükleniyor...");

                // Sembol listesi oluştur (tickerData üzerinden mevcut coinler)
                if (tickerData && Array.isArray(tickerData) && tickerData.length > 0) {
                    // Tüm sembollerin listesini çıkar
                    const symbols = tickerData
                        .filter(ticker => ticker.s && ticker.s.endsWith('USDT')) // Sadece USDT çiftleri
                        .map(ticker => ticker.s);

                    console.log(`💹 MA hesaplaması için ${symbols.length} sembol bulundu.`);

                    // MA verilerini yükle
                    const successCount = await initializeSymbols(symbols);
                    console.log(`✅ MA başlatma tamamlandı: ${successCount}/${symbols.length}`);

                    setSymbolsInitialized(true);
                } else {
                    console.log("❌ Sembol listesi oluşturulamadı: WebSocket verileri eksik");

                    // Kullanıcıya bilgi ver
                    toast({
                        title: "Veri Bağlantısı Sorunu",
                        description: "Fiyat verileri henüz yüklenemedi. Lütfen bağlantınızı kontrol edin ve tekrar deneyin.",
                        variant: "destructive",
                    });

                    return; // İşlemi başlatma
                }
            }

            // 💰 CRITICAL FIX: İşlemi başlatırken mevcut bakiyeyi trading initial balance olarak set et
            console.log(`💰 İşlem başlatılıyor - Mevcut bakiye trading initial balance olarak set ediliyor: ${accountBalance}`);
            setTradingStartInitialBalance(accountBalance);
            console.log(`✅ Trading initial balance set edildi: ${accountBalance} USDT`);

            // İşlemi başlat
            setAutoTradingEnabled(true);
            setAutoTradingStartTime(new Date());
            setTradingStatus('active');
            console.log("✅ İşlem başlatıldı");

            // Kullanıcıya bilgi ver
            toast({
                title: "İşlem Başlatıldı",
                description: `Otomatik işlem sistemi aktif edildi. Başlangıç bakiyesi: ${accountBalance.toFixed(2)} USDT`,
                variant: "default",
            });
        } else {
            // İşlemi durdur
            const openTrades = trades.filter(t => t.status === 'open').length;

            if (openTrades > 0 && !stopNewTrades) {
                // Açık işlemler varsa kullanıcıyı uyar
                toast({
                    title: "Dikkat",
                    description: `${openTrades} açık işlem var. Sadece yeni işlemler durdurulacak.`,
                    variant: "destructive",
                });
                setStopNewTrades(true);
            } else {
                // İşlemi tamamen durdur
                setAutoTradingEnabled(false);
                setTradingStatus('inactive');
                setAutoTradingStartTime(null);

                // 💰 CRITICAL FIX: Trading initial balance'ı sıfırla
                setTradingStartInitialBalance(null);
                console.log("💰 Trading initial balance sıfırlandı");

                console.log("🛑 İşlem durduruldu");

                toast({
                    title: "İşlem Durduruldu",
                    description: "Otomatik işlem sistemi devre dışı bırakıldı.",
                    variant: "default",
                });
            }
        }
    };

    const handleNewTrade = (data: { direction: 'long' | 'short'; amount: number; leverage: number }) => {
        const currentPrice = getTradePriceForSymbol(selectedTradingSymbol);
        if (!currentPrice) return;

        if (data.amount > accountBalance) {
            toast({
                title: "Yetersiz Bakiye",
                description: "İşlem tutarı mevcut bakiyeden yüksek olamaz.",
                variant: "destructive",
            });
            return;
        }

        const hasOpenTrade = trades.some(t => t.symbol === selectedTradingSymbol && t.status === 'open');
        if (hasOpenTrade) {
            toast({
                title: "İşlem Açılamadı",
                description: `${selectedTradingSymbol} için zaten açık bir işlem bulunuyor.`,
                variant: "destructive",
            });
            return;
        }

        const tradeId = `trade-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

        // Calculate entry commission
        const entryCommission = data.amount * 0.0002;

        const newTrade: Trade = {
            id: tradeId,
            symbol: selectedTradingSymbol,
            entryPrice: currentPrice,
            quantity: data.amount * data.leverage,
            direction: data.direction,
            openTime: new Date(),
            status: 'open',
            leverage: data.leverage,
            investmentAmount: data.amount,
            entryCommission: entryCommission
        };

        setTrades(prev => [...prev, newTrade]);

        // Subscribe to more frequent updates for this symbol
        subscribeToSymbolUpdates(selectedTradingSymbol);

        // Deduct both investment amount AND commission from balance
        setAccountBalance(accountBalance - data.amount - entryCommission);

        setLastTradeTimeMap(prev => ({
            ...prev,
            [selectedTradingSymbol]: new Date()
        }));

        // Test modu simülasyonu kaldırıldı
    };

    // simulateTradeOutcome, calculateTargetPrice ve closeSimulatedTrade
    // fonksiyonları kaldırıldı - gerçek modda kullanılmıyor

    // Calculate trades in last 24 hours
    const tradesInLast24h = React.useMemo(() => {
        const now = new Date();
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        return trades.filter(trade => trade.openTime >= yesterday).length;
    }, [trades]);

    const getTopCoin = React.useMemo(() => {
        if (!tickerData || !Array.isArray(tickerData)) return null;

        let topGainer = { symbol: '', price: 0, priceChangePercent: -100, volume: 0 };

        tickerData.forEach(ticker => {
            const changePercent = parseFloat(ticker.P || '0');
            if (changePercent > topGainer.priceChangePercent) {
                topGainer = {
                    symbol: ticker.s || '',
                    price: parseFloat(ticker.c || '0'),
                    priceChangePercent: changePercent,
                    volume: parseFloat(ticker.q || '0')
                };
            }
        });

        return topGainer.symbol ? {
            ...topGainer,
            isTopGainer: true,
            lastTradeTime: lastTradeTimeMap[topGainer.symbol]
        } : null;
    }, [tickerData, lastTradeTimeMap]);

    // Add getLowestCoin to find the most declining coin
    const getLowestCoin = React.useMemo(() => {
        if (!tickerData || !Array.isArray(tickerData)) return null;

        let topLoser = { symbol: '', price: 0, priceChangePercent: 100, volume: 0 };

        tickerData.forEach(ticker => {
            const changePercent = parseFloat(ticker.P || '0');
            if (changePercent < topLoser.priceChangePercent) {
                topLoser = {
                    symbol: ticker.s || '',
                    price: parseFloat(ticker.c || '0'),
                    priceChangePercent: changePercent,
                    volume: parseFloat(ticker.q || '0')
                };
            }
        });

        return topLoser.symbol ? {
            ...topLoser,
            isLowest: true,
            lastTradeTime: lastTradeTimeMap[topLoser.symbol]
        } : null;
    }, [tickerData, lastTradeTimeMap]);

    const alertedCoinsList = React.useMemo(() => {
        if (!tickerData || !Array.isArray(tickerData)) return [];

        return tickerData
            .filter(ticker => {
                if (!ticker.s) return false;

                const symbol = ticker.s;
                const currentPrice = parseFloat(ticker.c || '0');
                const avgPrice = movingAverages[symbol]?.average;

                // Use configSettings.alertPercentage
                return avgPrice && currentPrice > avgPrice * (1 + configSettings.alertPercentage / 100);
            })
            .map(ticker => ({
                symbol: ticker.s || '',
                price: parseFloat(ticker.c || '0'),
                priceChangePercent: parseFloat(ticker.P || '0'),
                volume: parseFloat(ticker.q || '0'),
                isAlerted: true,
                lastTradeTime: lastTradeTimeMap[ticker.s || '']
            }));
    }, [tickerData, movingAverages, configSettings.alertPercentage, lastTradeTimeMap]);

    const openTrades = React.useMemo(() => {
        return trades.filter(trade => trade.status === 'open');
    }, [trades]);

    // YENİ: Tüm kriterleri sağlayan coinleri göster (sadece alarm verenleri değil)
    const fixedCoins = React.useMemo(() => {
        const coins: CoinCardData[] = [];

        // Öncelikle criteria eligible coinleri göster
        const criteriaArray = Array.from(criteriaEligibleCoins);
        criteriaArray.slice(0, 6).forEach(symbol => { // En fazla 6 coin göster
            if (tickerData && Array.isArray(tickerData)) {
                const ticker = tickerData.find(t => t.s === symbol);
                if (ticker) {
                    const lastCheck = lastCriteriaCheck[symbol];
                    coins.push({
                        symbol,
                        price: parseFloat(ticker.c || '0'),
                        priceChangePercent: parseFloat(ticker.P || '0'),
                        volume: parseFloat(ticker.q || '0'),
                        isAlerted: true, // Criteria sağlayan coinler için true
                        lastTradeTime: lastTradeTimeMap[symbol]
                    });
                }
            }
        });

        // Eğer criteria eligible coin yeterli değilse, varsayılan coinleri ekle
        if (coins.length < 2) {
            const defaultSymbols = ['BTCUSDT', 'ETHUSDT'];
            defaultSymbols.forEach(symbol => {
                if (!coins.find(c => c.symbol === symbol)) {
                    if (tickerData && Array.isArray(tickerData)) {
                        const ticker = tickerData.find(t => t.s === symbol);
                        if (ticker) {
                            coins.push({
                                symbol,
                                price: parseFloat(ticker.c || '0'),
                                priceChangePercent: parseFloat(ticker.P || '0'),
                                volume: parseFloat(ticker.q || '0'),
                                isAlerted: alertedCoins.has(symbol),
                                lastTradeTime: lastTradeTimeMap[symbol]
                            });
                        } else {
                            coins.push({
                                symbol,
                                price: 0,
                                priceChangePercent: 0,
                                volume: 0,
                                isAlerted: false,
                                lastTradeTime: lastTradeTimeMap[symbol]
                            });
                        }
                    }
                }
            });
        }

        return coins;
    }, [tickerData, criteriaEligibleCoins, lastCriteriaCheck, alertedCoins, lastTradeTimeMap]);

    const handleAiRecommendations = (recommendations: TradingRecommendations) => {
        setTradingDirection(recommendations.tradingDirection);
        setProfitTarget(recommendations.profitTarget);
        setStopLoss(recommendations.stopLoss);
        setTradeUnit(recommendations.tradeUnit);
        setLeverage(recommendations.leverage);

        toast({
            title: "AI Önerileri Uygulandı",
            description: recommendations.reason,
            variant: "default",
        });
    };

    useEffect(() => {
        if (isConnected && leverage > 0 && selectedTradingSymbol) {
            setAccountLeverage(selectedTradingSymbol, leverage);
        }
    }, [isConnected, leverage, selectedTradingSymbol, setAccountLeverage]);

    // İlk bakiye yükleme - sayfa açıldığında REST API ile
    useEffect(() => {
        const loadInitialBalance = async () => {
            if (!isConnected || initialBalanceLoaded || isLoadingInitialBalance) {
                console.log('[Dashboard] loadInitialBalance: Skipped.', { isConnected, initialBalanceLoaded, isLoadingInitialBalance });
                return;
            }

            console.log('[Dashboard] Loading initial balance via fetchInitialAccountBalance...');
            setIsLoadingInitialBalance(true);

            try {
                const balanceData = await binanceApi.fetchInitialAccountBalance();
                console.log('[Dashboard] loadInitialBalance - fetchInitialAccountBalance response:', balanceData);

                if (balanceData && balanceData.balance !== undefined && balanceData.availableBalance !== undefined) {
                    const totalBalance = balanceData.balance;
                    const availableBal = balanceData.availableBalance;

                    setInitialBalance(totalBalance);
                    setAccountBalance(availableBal);
                    setInitialBalanceLoaded(true);

                    const totalBalanceStr = Number(totalBalance).toFixed(2);
                    const availableBalStr = Number(availableBal).toFixed(2);

                    toast({
                        title: 'Bakiye Yüklendi (REST)',
                        description: `Toplam: $${totalBalanceStr}, Kullanılabilir: $${availableBalStr}`,
                        variant: 'default'
                    });

                    console.log(`[Dashboard] Initial balance loaded via REST: Total: ${totalBalance}, Available: ${availableBal}`);
                } else {
                    console.warn('[Dashboard] loadInitialBalance: Failed to get valid balance data from fetchInitialAccountBalance.', balanceData);
                }
            } catch (error) {
                console.error('[Dashboard] Error in loadInitialBalance:', error);
                toast({
                    title: 'Bakiye Yükleme Hatası (REST)',
                    description: 'İlk bakiye REST API ile yüklenemedi.',
                    variant: 'destructive'
                });
            } finally {
                setIsLoadingInitialBalance(false);
            }
        };

        if (isConnected && !initialBalanceLoaded && !isLoadingInitialBalance) {
            loadInitialBalance();
        }
    }, [isConnected, initialBalanceLoaded, isLoadingInitialBalance, setAccountBalance, setInitialBalance, toast]);

    // Initialize Advanced Trading Manager
    useEffect(() => {
        if (useNewAdvancedFeatures && !advancedTradingManager) {
            console.log('🚀 Initializing Advanced Trading Manager...');
            try {
                const manager = new AdvancedTradingManager(newAdvancedConfig);
                setAdvancedTradingManager(manager);

                toast({
                    title: "Advanced Features Enabled",
                    description: "Multi-indicator filtering, volume validation, coin scoring, and dynamic TP/SL are now active.",
                    variant: "default",
                });

                console.log('✅ Advanced Trading Manager initialized successfully');
            } catch (error) {
                console.error('❌ Failed to initialize Advanced Trading Manager:', error);
                toast({
                    title: "Advanced Features Error",
                    description: "Failed to initialize advanced trading features.",
                    variant: "destructive",
                });
                setUseNewAdvancedFeatures(false);
            }
        } else if (!useNewAdvancedFeatures && advancedTradingManager) {
            console.log('🛑 Shutting down Advanced Trading Manager...');
            advancedTradingManager.clearCaches();
            setAdvancedTradingManager(null);
            setLastAdvancedAnalysis({});

            toast({
                title: "Advanced Features Disabled",
                description: "Switched back to basic trading mode.",
                variant: "default",
            });
        }
    }, [useNewAdvancedFeatures, advancedTradingManager, newAdvancedConfig, toast]);

    // Update Advanced Trading Manager configuration
    useEffect(() => {
        if (advancedTradingManager && useNewAdvancedFeatures) {
            advancedTradingManager.updateConfig(newAdvancedConfig);
            console.log('🔧 Advanced Trading Manager configuration updated');
        }
    }, [advancedTradingManager, newAdvancedConfig, useNewAdvancedFeatures]);

    // Initialize Trading Service with user-specific API key
    useEffect(() => {
        if (currentUserApiKeys?.apiKey && currentUser) {
            console.log('🚀 Initializing Trading Service with user-specific API key...');
            console.log('🔑 Current credentials:', {
                apiKey: currentUserApiKeys.apiKey.substring(0, 8) + '...',
                user: currentUser.username
            });

            initializeTradingService(currentUserApiKeys.apiKey)
                .then(success => {
                    if (success) {
                        console.log('✅ Trading Service initialized successfully');
                        setIsConnected(true);
                        setConnectionStatus('connected');
                    } else {
                        console.error('❌ Trading Service initialization failed');
                        setIsConnected(false);
                        setConnectionStatus('error');
                    }
                })
                .catch(error => {
                    console.error('❌ Trading Service initialization error:', error);
                    setIsConnected(false);
                    setConnectionStatus('error');
                });
        }

        // Cleanup on unmount or user change
        return () => {
            if (currentUser) {
                console.log('🧹 Shutting down Trading Service...');
                shutdownTradingService();
            }
        };
    }, [currentUserApiKeys?.apiKey, currentUser]);

    // WebSocket balance update events dinle
    useEffect(() => {
        const handleBalanceUpdate = (event: CustomEvent) => {
            console.log('[Dashboard] WebSocket balance-update event received:', event.detail);

            if (event.detail && event.detail.assets) {
                const usdtAsset = event.detail.assets.find((a: any) => a.asset === 'USDT');
                if (usdtAsset && usdtAsset.availableBalance !== undefined) {
                    const newAvailableBalance = parseFloat(usdtAsset.availableBalance);
                    setAccountBalance(newAvailableBalance);
                    console.log(`[Dashboard] WebSocket balance-update: Account balance updated to ${newAvailableBalance}`);

                    if (!initialBalanceLoaded && usdtAsset.walletBalance !== undefined) {
                        const newTotalBalance = parseFloat(usdtAsset.walletBalance);
                        setInitialBalance(newTotalBalance);
                        setInitialBalanceLoaded(true);
                        console.log(`[Dashboard] WebSocket balance-update: Initial balance set to ${newTotalBalance} (as it wasn't loaded before)`);
                    }
                } else {
                    console.warn('[Dashboard] WebSocket balance-update: USDT asset or availableBalance missing in event detail.', event.detail);
                }
            } else {
                console.warn('[Dashboard] WebSocket balance-update: Detail or assets missing in event.', event.detail);
            }
        };

        const handleInitialBalanceLoadedEvent = (event: CustomEvent) => {
            console.log('[Dashboard] Custom initial-balance-loaded event received:', event.detail);

            if (event.detail && event.detail.balance !== undefined && event.detail.availableBalance !== undefined) {
                const totalBalance = event.detail.balance;
                const availableBal = event.detail.availableBalance;

                setInitialBalance(totalBalance);
                setAccountBalance(availableBal);
                if (!initialBalanceLoaded) setInitialBalanceLoaded(true);

                console.log(`[Dashboard] Custom initial-balance-loaded event: Initial: ${totalBalance}, Available: ${availableBal}`);
            } else {
                console.warn('[Dashboard] Custom initial-balance-loaded event: Detail, balance, or availableBalance missing.', event.detail);
            }
        };

        const handleInitialBalanceLoadFailedEvent = (event: CustomEvent) => {
            console.error('[Dashboard] Custom initial-balance-load-failed event received:', event.detail);
        };

        // 🔧 DÜZELTME: AccountInfo'dan gelen bakiye güncellemelerini dinle
        const handleAccountBalanceUpdated = (event: CustomEvent) => {
            console.log('📥 [Dashboard] AccountInfo balance update received:', event.detail);

            if (event.detail && event.detail.availableBalance !== undefined) {
                const newAvailableBalance = event.detail.availableBalance;
                const newTotalBalance = event.detail.totalBalance || newAvailableBalance;

                setAccountBalance(newAvailableBalance);

                if (!initialBalanceLoaded) {
                    setInitialBalance(newTotalBalance);
                    setInitialBalanceLoaded(true);
                    console.log(`✅ [Dashboard] Initial balance set from AccountInfo: ${newTotalBalance}`);
                }

                console.log(`✅ [Dashboard] Account balance updated from AccountInfo: ${newAvailableBalance}`);
            }
        };

        window.addEventListener('balance-update', handleBalanceUpdate as EventListener);
        window.addEventListener('initial-balance-loaded', handleInitialBalanceLoadedEvent as EventListener);
        window.addEventListener('initial-balance-load-failed', handleInitialBalanceLoadFailedEvent as EventListener);
        window.addEventListener('account-balance-updated', handleAccountBalanceUpdated as EventListener);

        return () => {
            window.removeEventListener('balance-update', handleBalanceUpdate as EventListener);
            window.removeEventListener('initial-balance-loaded', handleInitialBalanceLoadedEvent as EventListener);
            window.removeEventListener('initial-balance-load-failed', handleInitialBalanceLoadFailedEvent as EventListener);
            window.removeEventListener('account-balance-updated', handleAccountBalanceUpdated as EventListener);
        };
    }, [setAccountBalance, setInitialBalance, initialBalanceLoaded, toast]);

    // TradeMonitor'den gelen bakiye güncellemelerini işle
    const handleBalanceUpdateFromMonitor = (balances: MonitorAccountBalance[]) => {
        console.log('[Dashboard] TradeMonitor balance update received:', balances);

        try {
            // USDT bakiyesini bul
            const usdtBalance = balances.find(b => b.asset === 'USDT');

            if (usdtBalance) {
                // Kullanılabilir bakiyeyi güncelle (free veya availableBalance)
                if (usdtBalance.availableBalance) {
                    const newAvailableBalance = parseFloat(usdtBalance.availableBalance);
                    setAccountBalance(newAvailableBalance);
                    console.log(`[Dashboard] TradeMonitor balance update: Account balance updated to ${newAvailableBalance}`);

                    // Custom event ile tüm uygulamaya bildir
                    window.dispatchEvent(new CustomEvent('balance-update-from-dashboard', {
                        detail: {
                            availableBalance: newAvailableBalance,
                            timestamp: Date.now()
                        }
                    }));
                } else if (usdtBalance.free) {
                    const newAvailableBalance = parseFloat(usdtBalance.free);
                    setAccountBalance(newAvailableBalance);
                    console.log(`[Dashboard] TradeMonitor balance update: Account balance updated to ${newAvailableBalance} (from free)`);

                    // Custom event ile tüm uygulamaya bildir
                    window.dispatchEvent(new CustomEvent('balance-update-from-dashboard', {
                        detail: {
                            availableBalance: newAvailableBalance,
                            timestamp: Date.now()
                        }
                    }));
                }

                // Unrealized Profit (P&L) değerini kontrol et
                const unrealizedProfit = usdtBalance.unrealizedProfit ? parseFloat(usdtBalance.unrealizedProfit) : 0;
                if (unrealizedProfit !== 0) {
                    // P&L değerini state'e ekle (bu Dashboard'da gösterilmek üzere)
                    // setUnrealizedProfit fonksiyonu yoksa, oluşturmak gerekir
                    console.log(`[Dashboard] Unrealized P&L updated: ${unrealizedProfit}`);

                    // Gerçekleşmemiş kar/zarar değerini Window event olarak da gönder
                    window.dispatchEvent(new CustomEvent('unrealized-pnl-updated', {
                        detail: {
                            value: unrealizedProfit,
                            timestamp: Date.now()
                        }
                    }));
                }

                // Eğer daha önce initial balance yüklenmediyse, yükle
                if (!initialBalanceLoaded) {
                    if (usdtBalance.walletBalance) {
                        const newTotalBalance = parseFloat(usdtBalance.walletBalance);
                        setInitialBalance(newTotalBalance);
                        setInitialBalanceLoaded(true);
                        console.log(`[Dashboard] TradeMonitor balance update: Initial balance set to ${newTotalBalance}`);
                    } else if (usdtBalance.free) {
                        // Wallet balance yoksa free'yi kullan
                        const newTotalBalance = parseFloat(usdtBalance.free);
                        setInitialBalance(newTotalBalance);
                        setInitialBalanceLoaded(true);
                        console.log(`[Dashboard] TradeMonitor balance update: Initial balance set to ${newTotalBalance} (from free)`);
                    }
                }
            } else {
                console.warn('[Dashboard] TradeMonitor balance update: USDT asset not found in balances', balances);
            }
        } catch (error) {
            console.error('[Dashboard] Error handling TradeMonitor balance update:', error);
        }
    };

    // TradeMonitor'den gelen işlem güncellemelerini işle
    const handleTradeUpdateFromMonitor = (updatedTrade: TradeUpdate) => {
        console.log('[Dashboard] TradeMonitor trade update received:', updatedTrade);

        // Güncellenmiş işlemi trades dizisinde bul ve güncelle
        const updatedTrades = trades.map(trade =>
            trade.id === updatedTrade.id ? { ...trade, ...updatedTrade } : trade
        );

        // Trades dizisini güncelle
        setTrades(updatedTrades);
    };

    // WebSocket kline verilerini işle (mum kapanışları için)
    useEffect(() => {
        if (!klineData) return;

        // Eğer klineData tek bir obje ise
        if (!Array.isArray(klineData) && klineData.k && klineData.s) {
            const symbol = klineData.s;
            const kline = klineData.k;
            const isClosed = kline.x === true;
            const closePrice = parseFloat(kline.c);

            if (isClosed && closePrice > 0) {
                console.log(`📊 [KAPANAN MUM] ${symbol}: ${closePrice}`);
                addPrice(symbol, closePrice, true);
            }

            return;
        }

        // Eğer klineData bir dizi ise
        if (Array.isArray(klineData)) {
            klineData.forEach(data => {
                if (data.s && data.c) {
                    const symbol = data.s;
                    const price = parseFloat(data.c);

                    // Mum kapanış işaretleyicisi doğrudan bilgi olarak gelmediğinden,
                    // ticker@arr formatından sadece fiyat güncelleme yapıyoruz
                    if (price > 0) {
                        addPrice(symbol, price, false);
                    }
                }
            });
        }
    }, [klineData, addPrice]);

    // Ticker verileri ile fiyat güncellemesi ve işlem sinyali kontrolü
    useEffect(() => {
        if (!tickerData || !Array.isArray(tickerData) || !autoTradingEnabled || stopNewTrades) return;

        // Kriterleri karşılayan coinleri takip etme
        const newEligibleCoins = new Set<string>();

        tickerData.forEach(ticker => {
            const symbol = ticker.s;
            const price = parseFloat(ticker.c || '0');

            if (!symbol || isNaN(price) || price <= 0) return;

            // MA verilerini kontrol et
            const maData = movingAverages[symbol];

            if (maData && maData.average && maData.history.length >= configSettings.movingAveragePoints) {
                const avg = maData.average;
                const priceChangePercent = ((price - avg) / avg) * 100;

                // Artık doğrudan alert değerini kullanıyoruz, kaldıraç etkisi hesaplanmıyor
                if (priceChangePercent > configSettings.alertPercentage) {
                    // Hacim kontrolü
                    const volume24h = parseFloat(ticker.q || '0');
                    const volumeInMillions = volume24h / 1000000;

                    if (volumeInMillions >= configSettings.minVolumeForTrade) {
                        // Diğer kriterleri kontrol et
                        const tradeCheck = checkTradeRestrictions(symbol, tradingDirection);

                        if (tradeCheck.allowed) {
                            // Use Advanced Trading Manager if enabled
                            if (useNewAdvancedFeatures && advancedTradingManager) {
                                try {
                                    // Prepare data for advanced analysis
                                    const prices = maData.history.map(h => h.price);
                                    const volumes = Array(prices.length).fill(volume24h); // Simplified volume array
                                    const timestamps = maData.history.map(h => h.timestamp);

                                    // Analyze trading opportunity with advanced features
                                    const decision = advancedTradingManager.analyzeTradingOpportunity(
                                        symbol,
                                        prices,
                                        volumes,
                                        timestamps,
                                        price,
                                        avg,
                                        tradingDirection
                                    );

                                    // Store advanced analysis result
                                    setLastAdvancedAnalysis(prev => ({
                                        ...prev,
                                        [symbol]: decision
                                    }));

                                    if (decision.shouldTrade) {
                                        console.log(`🚨 [ADVANCED SIGNAL] ${symbol}: Confidence=${decision.confidence.toFixed(1)}%, Reasons=[${decision.reasons.join(', ')}]`);
                                        newEligibleCoins.add(symbol);

                                        // Store advanced criteria check
                                        setLastCriteriaCheck(prev => ({
                                            ...prev,
                                            [symbol]: {
                                                timestamp: Date.now(),
                                                signal: tradingDirection,
                                                confidence: decision.confidence,
                                                reasons: decision.reasons,
                                                blockingReasons: decision.blockingReasons,
                                                canTrade: true
                                            }
                                        }));
                                    } else {
                                        console.log(`🚫 [ADVANCED FILTER] ${symbol}: Blocked - ${decision.blockingReasons.join(', ')}`);
                                        setLastCriteriaCheck(prev => ({
                                            ...prev,
                                            [symbol]: {
                                                timestamp: Date.now(),
                                                signal: null,
                                                confidence: decision.confidence,
                                                reasons: decision.reasons,
                                                blockingReasons: decision.blockingReasons,
                                                canTrade: false
                                            }
                                        }));
                                    }
                                } catch (error) {
                                    console.error(`❌ Advanced analysis error for ${symbol}:`, error);
                                    // Fallback to basic analysis
                                    console.log(`🚨 [BASIC SIGNAL] ${symbol}: Fiyat=${price}, MA=${avg.toFixed(6)}, Fark=%${priceChangePercent.toFixed(3)} > Eşik=%${Number(configSettings.alertPercentage).toFixed(3)}`);
                                    newEligibleCoins.add(symbol);
                                    setLastCriteriaCheck(prev => ({
                                        ...prev,
                                        [symbol]: {
                                            timestamp: Date.now(),
                                            signal: tradingDirection,
                                            confidence: Math.min(100, Math.floor(priceChangePercent * 20)),
                                            reasons: [
                                                `MA üzerinde %${priceChangePercent.toFixed(2)}`,
                                                `Hacim: ${volumeInMillions.toFixed(2)}M`
                                            ],
                                            blockingReasons: [],
                                            canTrade: true
                                        }
                                    }));
                                }
                            } else {
                                // Basic signal detection (original logic)
                                console.log(`🚨 [BASIC SIGNAL] ${symbol}: Fiyat=${price}, MA=${avg.toFixed(6)}, Fark=%${priceChangePercent.toFixed(3)} > Eşik=%${Number(configSettings.alertPercentage).toFixed(3)}`);
                                newEligibleCoins.add(symbol);

                                // Son kontrol bilgilerini kaydet
                                setLastCriteriaCheck(prev => ({
                                    ...prev,
                                    [symbol]: {
                                        timestamp: Date.now(),
                                        signal: tradingDirection,
                                        confidence: Math.min(100, Math.floor(priceChangePercent * 20)),
                                        reasons: [
                                            `MA üzerinde %${priceChangePercent.toFixed(2)}`,
                                            `Hacim: ${volumeInMillions.toFixed(2)}M`
                                        ],
                                        blockingReasons: [],
                                        canTrade: true
                                    }
                                }));
                            }

                            // Otomatik işlem açma koşulları kontrol ediliyor
                            if (autoTradingEnabled && !stopNewTrades) {
                                // Gerçek Binance pozisyonlarını kullan
                                const realOpenPositionsCount = (window as any).realOpenPositionsCount || 0;
                                const localOpenTrades = trades.filter(t => t.status === 'open').length;
                                const totalOpenPositions = Math.max(realOpenPositionsCount, localOpenTrades);

                                if (totalOpenPositions < configSettings.maxOpenPositions) {
                                    // Aynı coin'e işlem açılmasını önle
                                    const openPositionSymbols = (window as any).openPositionSymbols || [];
                                    if (openPositionSymbols.includes(symbol)) {
                                        console.log(`🚫 AUTO TRADE DUPLICATE PREVENTION: ${symbol} için zaten açık pozisyon var`);
                                        return; // Bu coin için işlem açma
                                    }

                                    // İşlem açılabilir
                                    const now = Date.now();
                                    const lastTradeTime = lastTradeTimeMap[symbol] || 0;

                                    // 🔧 DÜZELTME: Son işlemden beri minimum 1 dakika geçti mi?
                                    const lastTradeDate = lastTradeTimeMap[symbol] || new Date(0);
                                    const timeSinceLastTrade = now - lastTradeDate.getTime();
                                    const MIN_AUTO_TRADE_INTERVAL = 1 * 60 * 1000; // 1 dakika

                                    if (timeSinceLastTrade > MIN_AUTO_TRADE_INTERVAL) {
                                        // İşlem açma sinyali gönder
                                        console.log(`🔄 [OTOMATİK İŞLEM] ${symbol} için işlem açılıyor... (${Math.floor(timeSinceLastTrade / 60000)} dakika önce son işlem)`);

                                        // openTradeForSymbol kendi lock sistemini kullanacak
                                        openTradeForSymbol(symbol, price);
                                    } else {
                                        const remainingMinutes = Math.ceil((MIN_AUTO_TRADE_INTERVAL - timeSinceLastTrade) / 60000);
                                        console.log(`🚫 AUTO TRADE RATE LIMIT: ${symbol} için ${remainingMinutes} dakika daha beklemeli`);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });

        // Kriterleri karşılayan coinleri güncelle
        setCriteriaEligibleCoins(newEligibleCoins);

        // Alarm durumundaki tüm coinleri takip et (display için)
        const newAlertedCoins = new Set<string>();

        tickerData.forEach(ticker => {
            const symbol = ticker.s;
            const price = parseFloat(ticker.c || '0');
            const maData = movingAverages[symbol];

            if (maData && maData.average) {
                const priceChangePercent = ((price - maData.average) / maData.average) * 100;

                if (priceChangePercent >= configSettings.alertPercentage) {
                    newAlertedCoins.add(symbol);
                }
            }
        });

        // Alarmdaki coinleri güncelle
        setAlertedCoins(newAlertedCoins);

    }, [tickerData, autoTradingEnabled, stopNewTrades, movingAverages, tradingDirection,
        configSettings.alertPercentage, configSettings.movingAveragePoints, configSettings.minVolumeForTrade,
        leverage, checkTradeRestrictions, trades.length, // trades.length yerine trades kullanarak gereksiz re-render'ları azalt
        configSettings.maxOpenPositions]); // openTradeForSymbol ve lastTradeTimeMap dependency'den çıkarıldı

    // Global trades listesini WebSocket servisi için erişilebilir yap
    useEffect(() => {
        (window as any).globalTrades = trades;
    }, [trades]);

    // 🔄 CRITICAL: Pozisyon Senkronizasyon Sistemi
    useEffect(() => {
        const syncPositionsWithBinance = async () => {
            try {
                // Gerçek Binance pozisyonlarını al
                const realPositions = (window as any).realPositions || [];
                const realOpenSymbols = realPositions.map((pos: any) => pos.s || pos.symbol);

                console.log(`🔄 POSITION SYNC: ${realOpenSymbols.length} gerçek açık pozisyon tespit edildi: [${realOpenSymbols.join(', ')}]`);

                // Lokal trade'lerde açık olan ama Binance'de kapalı olan işlemleri bul
                const localOpenTrades = trades.filter(t => t.status === 'open');
                const tradesToClose: string[] = [];

                localOpenTrades.forEach(trade => {
                    const isReallyOpen = realOpenSymbols.includes(trade.symbol);
                    if (!isReallyOpen) {
                        console.log(`🚨 SYNC ISSUE: ${trade.symbol} lokal olarak açık ama Binance'de kapalı!`);
                        tradesToClose.push(trade.id);
                    }
                });

                // Senkronizasyon gerekli olan trade'leri kapat
                if (tradesToClose.length > 0) {
                    console.log(`🔄 SYNCING: ${tradesToClose.length} trade kapatılıyor...`);

                    setTrades(prev => prev.map(trade => {
                        if (tradesToClose.includes(trade.id)) {
                            console.log(`✅ SYNC CLOSE: ${trade.symbol} trade'i senkronize edildi`);

                            const currentPrice = getTradePriceForSymbol(trade.symbol);
                            const priceChange = trade.direction === 'long'
                                ? (currentPrice - trade.entryPrice) / trade.entryPrice * 100
                                : (trade.entryPrice - currentPrice) / trade.entryPrice * 100;

                            const leveragedProfitLoss = priceChange * (trade.leverage || 1);
                            const investmentAmount = trade.investmentAmount || 0;
                            const rawProfitAmount = (investmentAmount * leveragedProfitLoss) / 100;
                            const exitAmount = Math.max(0, investmentAmount + rawProfitAmount);
                            const exitCommission = exitAmount * 0.0002;
                            const entryCommission = trade.entryCommission || (investmentAmount * 0.0002);
                            const totalCommission = entryCommission + exitCommission;
                            const actualProfitAmount = rawProfitAmount - totalCommission;

                            return {
                                ...trade,
                                status: 'closed' as const,
                                exitPrice: currentPrice,
                                exitTime: new Date(),
                                profitLoss: leveragedProfitLoss,
                                actualProfitAmount: actualProfitAmount,
                                entryCommission: entryCommission,
                                exitCommission: exitCommission,
                                commissionFee: totalCommission,
                                totalFees: totalCommission,
                                closeReason: 'sync_close' // Senkronizasyon nedeniyle kapatıldı
                            };
                        }
                        return trade;
                    }));

                    // Başarı bildirimi
                    toast({
                        title: "Pozisyon Senkronizasyonu ✅",
                        description: `${tradesToClose.length} kapalı pozisyon senkronize edildi.`,
                        variant: "default",
                    });
                }

            } catch (error) {
                console.error('❌ Pozisyon senkronizasyon hatası:', error);
            }
        };

        // İlk senkronizasyon
        syncPositionsWithBinance();

        // Her 10 saniyede bir senkronizasyon
        const syncInterval = setInterval(syncPositionsWithBinance, 10000);

        return () => clearInterval(syncInterval);
    }, [trades, getTradePriceForSymbol, toast]);

    // 🔄 WebSocket bağlantısı kurulduğunda pozisyon sync'i başlat
    useEffect(() => {
        if (isConnected && isRealTrading) {
            // WebSocket API instance'ını al ve pozisyon sync'i başlat
            const wsAPI = (window as any).webSocketAPI;
            if (wsAPI && wsAPI.startPositionSync) {
                wsAPI.startPositionSync();
                console.log('🔄 Dashboard: Pozisyon senkronizasyon sistemi başlatıldı');
            }
        }
    }, [isConnected, isRealTrading]);

    // Binance Position Update Event'lerini dinle
    useEffect(() => {
        const handlePositionsUpdated = (event: CustomEvent) => {
            const { openPositions, openCount, symbols, userApiKey } = event.detail;

            // User context validation - prevent cross-contamination
            if (userApiKey) {
                const currentUserApiKey = currentUserApiKeys?.apiKey?.substring(0, 8) + '...';
                if (userApiKey !== currentUserApiKey) {
                    console.warn(`🚫 DASHBOARD: Position event ignored - wrong user context. Event: ${userApiKey}, Current: ${currentUserApiKey}`);
                    return;
                }
                console.log(`✅ DASHBOARD: Position event accepted - correct user context: ${currentUserApiKey}`);
            }

            console.log(`📊 DASHBOARD POSITION UPDATE: ${openCount} açık pozisyon, Symbols: [${symbols.join(', ')}]`);

            // Aynı coin'e işlem açılmasını önlemek için kontrol
            symbols.forEach((symbol: string) => {
                console.log(`🚫 DUPLICATE PREVENTION: ${symbol} için zaten açık pozisyon var`);
            });
        };

        // Event listener ekle
        window.addEventListener('positions-updated', handlePositionsUpdated as EventListener);

        // Cleanup
        return () => {
            window.removeEventListener('positions-updated', handlePositionsUpdated as EventListener);
        };
    }, [currentUserApiKeys]);

    // Timer functionality for current time and trading duration
    useEffect(() => {
        const timer = setInterval(() => {
            const now = new Date();
            // setCurrentTime(now); // Removed - not needed without DashboardHeader

            // Calculate trading duration if trading is active
            if (autoTradingStartTime) {
                const diffMs = now.getTime() - autoTradingStartTime.getTime();
                const hours = Math.floor(diffMs / (1000 * 60 * 60));
                const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

                const formattedDuration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                setTradingDuration(formattedDuration);
            } else {
                setTradingDuration("00:00:00");
            }
        }, 1000);

        return () => clearInterval(timer);
    }, [autoTradingStartTime]);

    // Otomatik aktif emir temizleme sistemi
    useEffect(() => {
        const autoCleanup = () => {
            // Eğer açık işlem yoksa ve aktif emirler varsa temizle
            const openTrades = trades.filter(t => t.status === 'open');

            if (openTrades.length === 0 && (window as any).clearActiveOrders) {
                console.log('🧹 AUTO CLEANUP: Açık işlem yok, aktif emirler temizleniyor');
                (window as any).clearActiveOrders();
            }
        };

        // Her 60 saniyede bir otomatik temizlik
        const interval = setInterval(autoCleanup, 60000);

        // İlk temizliği hemen yap
        autoCleanup();

        return () => clearInterval(interval);
    }, [trades]);

    // TP/SL ile tamamlanan işlemleri dinle
    useEffect(() => {
        const handleTradeCompleted = (event: CustomEvent) => {
            const tradeRecord = event.detail;
            console.log(`📝 TP/SL ile işlem tamamlandı:`, tradeRecord);

            // Trade formatına dönüştür
            const completedTrade: Trade = {
                id: tradeRecord.id,
                symbol: tradeRecord.symbol,
                direction: tradeRecord.direction,
                entryPrice: tradeRecord.entryPrice,
                exitPrice: tradeRecord.exitPrice,
                quantity: tradeRecord.quantity,
                openTime: new Date(tradeRecord.timestamp),
                exitTime: new Date(tradeRecord.timestamp),
                status: 'closed',
                profitLoss: tradeRecord.pnl,
                leverage: 20, // Default leverage
                investmentAmount: 0.3, // Default investment amount
                actualProfitAmount: tradeRecord.pnl,
                commissionFee: 0, // Will be calculated
                fundingFee: 0,
                totalFees: 0
            };

            // İşlem geçmişine ekle
            setTrades(prevTrades => {
                // Önce aynı sembol için açık işlem var mı kontrol et
                const openTradeIndex = prevTrades.findIndex(t =>
                    t.symbol === tradeRecord.symbol && t.status === 'open'
                );

                if (openTradeIndex !== -1) {
                    // Açık işlemi kapat
                    const updatedTrades = [...prevTrades];
                    updatedTrades[openTradeIndex] = {
                        ...updatedTrades[openTradeIndex],
                        status: 'closed',
                        exitPrice: tradeRecord.exitPrice,
                        exitTime: new Date(tradeRecord.timestamp),
                        profitLoss: tradeRecord.pnl,
                        actualProfitAmount: tradeRecord.pnl
                    };
                    return updatedTrades;
                } else {
                    // Yeni tamamlanmış işlem ekle
                    return [...prevTrades, completedTrade];
                }
            });

            // Toast bildirim
            toast({
                title: tradeRecord.closeReason === 'take_profit' ? "Take Profit Gerçekleşti! 🎯" : "Stop Loss Gerçekleşti! 🛑",
                description: `${tradeRecord.symbol} pozisyonu ${tradeRecord.closeReason === 'take_profit' ? 'kar alarak' : 'zarar keserek'} kapandı. PNL: $${tradeRecord.pnl?.toFixed(2) || '0.00'}`,
                variant: tradeRecord.closeReason === 'take_profit' ? "default" : "destructive",
            });
        };

        // Event listener ekle
        window.addEventListener('trade-completed', handleTradeCompleted as EventListener);

        // Cleanup
        return () => {
            window.removeEventListener('trade-completed', handleTradeCompleted as EventListener);
        };
    }, [toast]);

    return (
        <div className="w-full mx-0 p-0 overflow-x-hidden">

            <DashboardHeader
                title="ParaBOT"
                soundEnabled={true}
                onToggleSound={() => {}}
                currentTime={new Date()}
                connectionStatus="open"
            />

            <div className="container mx-auto px-2 md:px-4 lg:px-6 max-w-full mt-1">
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-2 lg:gap-4">
                    {/* Sol panel - Ayarlar ve kontroller */}
                    <div className="lg:col-span-3 space-y-2">
                        <AccountInfo
                            balance={accountBalance}
                            initialBalance={tradingStartInitialBalance || initialBalance}
                            stats={tradeStats}
                            tradingDuration={autoTradingEnabled ? tradingDuration : null}
                            trades={trades}
                            onRefresh={() => {
                                // Güvenli refresh fonksiyonu - circular dependency'yi önler
                                console.log('🔄 [Dashboard] Manual refresh requested');
                                if (fetchAccountInfo) {
                                    fetchAccountInfo();
                                }
                            }}
                            accountInfo={accountInfo}
                        />

                        <div className="space-y-2 mb-2 mt-2">
                            <div className="flex items-center space-x-2">
                                
                                {advancedTradingInitialized && (
                                    <Badge variant="outline" className="bg-green-50">
                                        Aktif
                                    </Badge>
                                )}
                            </div>
                            {useAdvancedTrading && (
                                <p className="text-xs text-muted-foreground">
                                    ISOLATED mod, otomatik TP/SL ve trailing stop desteği
                                </p>
                            )}

                            <div className="flex items-center space-x-2 mt-2">
                                <input
                                    type="checkbox"
                                    id="useNewAdvancedFeatures"
                                    checked={useNewAdvancedFeatures}
                                    onChange={(e) => setUseNewAdvancedFeatures(e.target.checked)}
                                    disabled={autoTradingEnabled}
                                    className="rounded"/>
                                <Label htmlFor="useNewAdvancedFeatures" className="text-sm">
                                    Enhanced Analysis (RSI+MACD+Volume+ATR)
                                </Label>
                                {advancedTradingManager && (
                                    <Badge variant="outline" className="bg-blue-50">
                                        Enhanced
                                    </Badge>
                                )}
                            </div>
                            {useNewAdvancedFeatures && (
                                <p className="text-xs text-muted-foreground">
                                    Multi-indicator filtering, volume validation, coin scoring, and dynamic TP/SL
                                </p>
                            )}
                            <div className="space-y-2">
                                {/* Bakiye Debug Bilgisi */}
                                <div className="text-xs text-gray-500 p-2 bg-gray-50 rounded">
                                    Debug: Balance={accountBalance}, TradeUnit={tradeUnit}, InitialLoaded={initialBalanceLoaded ? 'Yes' : 'No'}
                                </div>

                                {/* Manuel Bakiye Yükleme Butonu */}
                                {(accountBalance === 0 || !initialBalanceLoaded) && (
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            console.log('🔄 [Button] Manuel bakiye yükleme butonuna tıklandı');
                                            if (fetchAccountInfo) {
                                                fetchAccountInfo();
                                            }
                                        }}
                                        className="w-full"
                                    >
                                        Bakiye Yükle
                                    </Button>
                                )}

                                {/* 🔧 DÜZELTME: Debug butonu - 401 hatası için */}
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        console.log('🔍 [DEBUG] Authentication Debug Info:', {
                                            currentUser: currentUser?.username || 'none',
                                            hasCurrentUserApiKeys: !!currentUserApiKeys,
                                            apiKey: currentUserApiKeys?.apiKey ? currentUserApiKeys.apiKey.substring(0, 8) + '...' : 'none',
                                            apiSecret: currentUserApiKeys?.apiSecret ? 'present' : 'none',
                                            accountBalance,
                                            initialBalanceLoaded
                                        });

                                        // Session durumunu kontrol et
                                        console.log('🔍 [DEBUG] Testing credentials API...');
                                        fetch('/api/credentials/' + (currentUser?.username || 'unknown'), {
                                            credentials: 'include', // Cookie'leri dahil et
                                            headers: {
                                                'Content-Type': 'application/json'
                                            }
                                        })
                                            .then(response => {
                                                console.log('🔍 [DEBUG] Credentials API Response Status:', response.status);
                                                console.log('🔍 [DEBUG] Credentials API Response Headers:', {
                                                    'set-cookie': response.headers.get('set-cookie'),
                                                    'content-type': response.headers.get('content-type')
                                                });
                                                if (response.ok) {
                                                    return response.json();
                                                } else {
                                                    return response.json().catch(() => ({ error: 'No JSON response' }));
                                                }
                                            })
                                            .then(data => {
                                                if (data.error) {
                                                    console.error('🔍 [DEBUG] Credentials API Error Response:', data);
                                                } else {
                                                    console.log('🔍 [DEBUG] Credentials API Success Response:', {
                                                        hasApiKey: !!data.apiKey,
                                                        apiKey: data.apiKey ? data.apiKey.substring(0, 8) + '...' : 'none'
                                                    });
                                                }
                                            })
                                            .catch(error => {
                                                console.error('🔍 [DEBUG] Credentials API Network Error:', error);
                                            });

                                        // 🔧 DÜZELTME: Session durumunu test et
                                        console.log('🔍 [DEBUG] Testing session status...');
                                        fetch('/api/auth/status', {
                                            credentials: 'include',
                                            headers: {
                                                'Content-Type': 'application/json'
                                            }
                                        })
                                            .then(response => {
                                                console.log('🔍 [DEBUG] Session Status Response:', response.status);
                                                return response.json();
                                            })
                                            .then(data => {
                                                console.log('🔍 [DEBUG] Session Status Data:', data);
                                            })
                                            .catch(error => {
                                                console.error('🔍 [DEBUG] Session Status Error:', error);
                                            });

                                        // 🔧 DÜZELTME: Test API çağrısı yap
                                        console.log('🔍 [DEBUG] Testing Binance API call...');
                                        fetch('/api/binance/fapi/v2/account', {
                                            credentials: 'include',
                                            headers: {
                                                'Content-Type': 'application/json'
                                            }
                                        })
                                            .then(response => {
                                                console.log('🔍 [DEBUG] Binance API Response Status:', response.status);
                                                if (response.ok) {
                                                    return response.json();
                                                } else {
                                                    return response.json().catch(() => ({ error: `HTTP ${response.status}` }));
                                                }
                                            })
                                            .then(data => {
                                                if (data.error) {
                                                    console.error('🔍 [DEBUG] Binance API Error Response:', data);
                                                } else {
                                                    console.log('🔍 [DEBUG] Binance API Success Response:', {
                                                        hasData: !!data,
                                                        totalWalletBalance: data.totalWalletBalance || 'N/A'
                                                    });
                                                }
                                            })
                                            .catch(error => {
                                                console.error('🔍 [DEBUG] Binance API Network Error:', error);
                                            });
                                    }}
                                    className="w-full text-xs"
                                >
                                    Debug Auth (401 Fix)
                                </Button>

                                {/* 🔧 DÜZELTME: Manuel Re-login butonu */}
                                <Button
                                    variant="destructive"
                                    onClick={() => {
                                        console.log('🔄 [DEBUG] Manual re-login triggered');
                                        // Logout ve tekrar login
                                        logout();
                                        setTimeout(() => {
                                            window.location.href = '/login';
                                        }, 500);
                                    }}
                                    className="w-full text-xs"
                                >
                                    Re-Login (401 Fix)
                                </Button>

                                <div className="grid grid-cols-3 gap-1">
                                    <Button
                                        variant={autoTradingEnabled ? 'destructive' : 'default'}
                                        onClick={() => {
                                            console.log('🔄 [Button] İşlemi Başlat/Durdur butonuna tıklandı');
                                            handleToggleAutoTrading();
                                        }}
                                        disabled={autoTradingEnabled && trades.some(t => t.status === 'open') && !stopNewTrades}
                                        className="col-span-2"
                                    >
                                        {autoTradingEnabled ? 'İşlemi Durdur' : 'İşlemi Başlat'}
                                    </Button>
                                    <Button
                                        variant={stopNewTrades ? "outline" : "default"}
                                        onClick={() => setStopNewTrades(!stopNewTrades)}
                                        disabled={!autoTradingEnabled}
                                    >
                                        {stopNewTrades ? "İşlem Açılsın" : "İşlem Açma"}
                                    </Button>
                                </div>
                            </div>
                        </div>

                        <UnifiedTradingSettings
                            leverage={leverage}
                            setLeverage={setLeverage}
                            tradeUnit={tradeUnit}
                            setTradeUnit={setTradeUnit}
                            profitTarget={profitTarget}
                            setProfitTarget={setProfitTarget}
                            stopLoss={stopLoss}
                            setStopLoss={setStopLoss}
                            alertPercentage={configSettings.alertPercentage}
                            setAlertPercentage={(value) => setConfigSettings(prev => ({ ...prev, alertPercentage: value }))}
                            movingAveragePoints={configSettings.movingAveragePoints}
                            setMovingAveragePoints={(value) => setConfigSettings(prev => ({ ...prev, movingAveragePoints: value }))}
                            maxOpenPositions={configSettings.maxOpenPositions}
                            setMaxOpenPositions={(value) => setConfigSettings(prev => ({ ...prev, maxOpenPositions: value }))}
                            isAutoTradingEnabled={autoTradingEnabled}
                        />

                        <AdvancedTradingConfig
                            config={newAdvancedConfig}
                            onConfigChange={(changes) => setNewAdvancedConfig(prev => ({ ...prev, ...changes }))}
                            isEnabled={useNewAdvancedFeatures}
                            disabled={autoTradingEnabled}
                        />

                        <AiTradeOptimizer
                            isEnabled={aiOptimizationEnabled}
                            onToggleEnabled={() => setAiOptimizationEnabled(prev => !prev)}
                            tradeStats={tradeStats}
                            tradingDirection={tradingDirection}
                            profitTarget={profitTarget}
                            stopLoss={stopLoss}
                            tradeUnit={tradeUnit}
                            leverage={leverage}
                            onApplyRecommendations={handleAiRecommendations}
                            lastAnalysisTime={lastAiAnalysisTime}
                            autoTradingEnabled={autoTradingEnabled}
                        />
                    </div>

                    {/* Sağ panel - Coinler ve işlemler */}
                    <div className="lg:col-span-9 space-y-2">
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                            {btcUsdtData && (
                                <CoinCard
                                    key={btcUsdtData.symbol}
                                    symbol={btcUsdtData.symbol}
                                    price={btcUsdtData.price}
                                    priceChangePercent={btcUsdtData.priceChangePercent}
                                    volume={btcUsdtData.volume}
                                    lastTradeTime={btcUsdtData.lastTradeTime}
                                    className="w-full"
                                />
                            )}
                            {ethUsdtData && (
                                <CoinCard
                                    key={ethUsdtData.symbol}
                                    symbol={ethUsdtData.symbol}
                                    price={ethUsdtData.price}
                                    priceChangePercent={ethUsdtData.priceChangePercent}
                                    volume={ethUsdtData.volume}
                                    lastTradeTime={ethUsdtData.lastTradeTime}
                                    className="w-full"
                                />
                            )}
                            {getTopCoin && (
                                <CoinCard
                                    key={getTopCoin.symbol}
                                    symbol={getTopCoin.symbol}
                                    price={getTopCoin.price}
                                    priceChangePercent={getTopCoin.priceChangePercent}
                                    volume={getTopCoin.volume}
                                    isTopGainer={true}
                                    lastTradeTime={getTopCoin.lastTradeTime}
                                    className="w-full"
                                />
                            )}
                            {getLowestCoin && (
                                <CoinCard
                                    key={getLowestCoin.symbol}
                                    symbol={getLowestCoin.symbol}
                                    price={getLowestCoin.price}
                                    priceChangePercent={getLowestCoin.priceChangePercent}
                                    volume={getLowestCoin.volume}
                                    isLowest={true}
                                    lastTradeTime={getLowestCoin.lastTradeTime}
                                    className="w-full"
                                />
                            )}
                        </div>

                        <div className="grid gap-4 mb-4">
                            <div>
                                <TechnicalSignals
                                    isActive={autoTradingEnabled}
                                    direction={tradingDirection}
                                    criteriaMetCoins={Array.from(criteriaEligibleCoins)}
                                    alertPercentage={configSettings.alertPercentage}
                                    movingAveragePoints={configSettings.movingAveragePoints}
                                    maxPositions={configSettings.maxOpenPositions}
                                    leverage={leverage}
                                    useAdvancedFeatures={useNewAdvancedFeatures}
                                    advancedAnalysis={lastAdvancedAnalysis}
                                />

                                <AutoTradingLogic
                                    autoTradingEnabled={autoTradingEnabled}
                                    tradingDirection={tradingDirection}
                                    stopNewTrades={stopNewTrades}
                                    marketData={tickerData}
                                    movingAverages={Object.fromEntries(
                                        Object.entries(movingAverages).map(([symbol, data]) => [
                                            symbol,
                                            {
                                                average: data.average,
                                                history: data.history.map(point => point.price)
                                            }
                                        ])
                                    )}
                                    configSettings={configSettings}
                                    tradingRestrictions={tradingRestrictions}
                                    newCoinsMap={newCoinsMap}
                                    hasNegativeFunding={hasNegativeFunding(tradingDirection)}
                                    isNearFundingTime={isNearFundingTime(tradingRestrictions.fundingTimeThresholdMinutes)}
                                    cumulativeProfitLoss={cumulativeProfitLoss}
                                    initialBalance={tradingStartInitialBalance || initialBalance}
                                    trades={trades}
                                    lastTradeTimeMap={lastTradeTimeMap}
                                    openTradeForSymbol={openTradeForSymbol}
                                    useAdvancedTrading={useAdvancedTrading}
                                    tradingConfig={{
                                        tradeAmount: tradeUnit,
                                        leverage: leverage,
                                        takeProfitPercent: profitTarget,
                                        stopLossPercent: stopLoss,
                                    }}
                                />
                            </div>
                        </div>

                        <Card className="animate-fade-in mt-4">
                            <CardHeader className="pb-1 pt-2">
                                <CardTitle className="flex items-center justify-between text-sm">
                                    <span>Tüm Kriterleri Sağlayan Coinler</span>
                                    <Badge>{criteriaEligibleCoins.size} Coin</Badge>
                                </CardTitle>
                                <CardDescription className="text-xs">
                                    MA crossover ve MACD sinyallerini karşılayan coinler
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-1">
                                <div className="min-h-[100px] max-h-[100px] overflow-y-auto">
                                    {criteriaEligibleCoins.size === 0 ? (
                                        <p className="text-center py-1 text-xs text-muted-foreground">
                                            Henüz tüm kriterleri sağlayan coin bulunmuyor.
                                        </p>
                                    ) : (
                                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-1">
                                            {Array.from(criteriaEligibleCoins).map((symbol) => {
                                                const ticker = tickerData?.find(t => t.s === symbol);
                                                const lastCheck = lastCriteriaCheck[symbol];

                                                if (!ticker) return null;

                                                return (
                                                    <div key={symbol} className="bg-green-50 border border-green-200 rounded-lg p-2">
                                                        <div className="font-semibold text-green-800 text-sm">{symbol}</div>
                                                        <div className="text-xs text-green-600">
                                                            ${parseFloat(ticker.c || '0').toFixed(4)}
                                                        </div>
                                                        <div className="text-xs text-green-500">
                                                            {lastCheck?.signal?.toUpperCase()} • {lastCheck?.confidence !== undefined ? `${Number(lastCheck.confidence).toFixed(0)}%` : '0%'}
                                                        </div>
                                                        <div className="text-xs text-green-400">
                                                            ✅ İşlem Açılabilir
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        <div className="mt-1">
                            <TradeMonitor
                                trades={trades}
                                closeTrade={closeTrade}
                                stopLoss={stopLoss}
                                profitTarget={profitTarget}
                                leverage={leverage}
                                getTradePriceForSymbol={getTradePriceForSymbol}
                                monitoringFrequency={monitoringFrequency}
                                credentials={isConnected ? {
                                    apiKey: '', // Kullanıcının API anahtarını buraya geçir
                                    apiSecret: ''
                                } : undefined}
                                onUpdateBalance={handleBalanceUpdateFromMonitor}
                                onUpdateTrade={handleTradeUpdateFromMonitor}
                            />
                        </div>

                        <div className="mt-4">
                            <TradeHistory trades={trades} />
                        </div>


                    </div>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;
